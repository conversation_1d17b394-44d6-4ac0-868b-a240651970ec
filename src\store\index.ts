import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import type { User, Order, GoldType, DashboardStats } from '../types';

export interface AppState {
  // 用户相关
  currentUser: User | null;
  isAuthenticated: boolean;
  
  // 数据相关
  orders: Order[];
  goldTypes: GoldType[];
  dashboardStats: DashboardStats | null;
  
  // UI状态
  loading: boolean;
  sidebarCollapsed: boolean;
  
  // 用户操作
  login: (user: User) => void;
  logout: () => void;
  
  // 数据操作
  setOrders: (orders: Order[]) => void;
  setGoldTypes: (types: GoldType[]) => void;
  setDashboardStats: (stats: DashboardStats) => void;
  
  // UI操作
  setLoading: (loading: boolean) => void;
  setSidebarCollapsed: (collapsed: boolean) => void;
  
  // 清除状态
  clearState: () => void;
  
  // 初始化认证状态
  initAuth: () => void;
}

export const useAppStore = create<AppState>()(
  persist(
    (set, get) => ({
      // 初始状态
      currentUser: null,
      isAuthenticated: false,
      orders: [],
      goldTypes: [],
      dashboardStats: null,
      loading: false,
      sidebarCollapsed: false,

      // 用户操作
      login: (user: User) => set({ 
        currentUser: user, 
        isAuthenticated: true 
      }),
      
      logout: () => set({ 
        currentUser: null, 
        isAuthenticated: false 
      }),

      // 数据操作
      setOrders: (orders: Order[]) => set({ orders }),
      setGoldTypes: (types: GoldType[]) => set({ goldTypes: types }),
      setDashboardStats: (stats: DashboardStats) => set({ dashboardStats: stats }),

      // UI操作
      setLoading: (loading: boolean) => set({ loading }),
      setSidebarCollapsed: (collapsed: boolean) => set({ sidebarCollapsed: collapsed }),

      // 清除状态
      clearState: () => set({
        currentUser: null,
        isAuthenticated: false,
        orders: [],
        goldTypes: [],
        dashboardStats: null,
        loading: false,
        sidebarCollapsed: false,
      }),

      // 初始化认证状态
      initAuth: () => {
        const state = get();
        // 如果有用户信息但没有认证状态，自动设置认证状态
        if (state.currentUser && !state.isAuthenticated) {
          set({ isAuthenticated: true });
        }
      },
    }),
    {
      name: 'gold-recovery-store', // localStorage 中的 key
      storage: createJSONStorage(() => localStorage),
      // 只持久化认证相关状态，数据状态不持久化
      partialize: (state) => ({
        currentUser: state.currentUser,
        isAuthenticated: state.isAuthenticated,
        sidebarCollapsed: state.sidebarCollapsed,
      }),
      // 版本控制，便于后续升级
      version: 1,
      // 迁移函数，处理旧版本数据
      migrate: (persistedState: any, version: number) => {
        if (version === 0) {
          // 处理版本0到版本1的迁移
          return {
            ...persistedState,
            version: 1,
          };
        }
        return persistedState;
      },
    }
  )
); 