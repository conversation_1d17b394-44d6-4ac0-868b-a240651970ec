import axios from 'axios';
import type { AxiosInstance, AxiosResponse } from 'axios';
import type { 
  User, 
  Order, 
  Quote, 
  GoldType, 
  VideoInspection, 
  GoldPrice, 
  DashboardStats,
  ApiResponse,
  PaginationParams,
  SearchParams
} from '../types';

/**
 * 创建并配置Axios实例
 * 包含基础URL、超时设置、请求/响应拦截器等
 */
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://192.168.1.68:8085',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

/**
 * 请求拦截器
 * 在每个请求发送前自动添加认证token
 */
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

/**
 * 响应拦截器
 * 统一处理响应数据和错误
 */
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

/**
 * 用户认证相关API
 */
export const authAPI = {
  /**
   * 用户登录
   * @param credentials 登录凭据（用户名和密码）
   * @returns 登录结果，包含用户信息和token
   */
  login: async (credentials: { username: string; password: string }): Promise<ApiResponse<{ user: User; token: string }>> => {
    const response = await api.post('/auth/login', credentials);
    return response.data;
  },

  /**
   * 用户注册
   * @param userData 用户注册信息
   * @returns 注册结果
   */
  register: async (userData: Partial<User>): Promise<ApiResponse<User>> => {
    const response = await api.post('/auth/register', userData);
    return response.data;
  },

  /**
   * 用户登出
   * @returns 登出结果
   */
  logout: async (): Promise<ApiResponse<void>> => {
    const response = await api.post('/auth/logout');
    return response.data;
  },

  /**
   * 刷新访问token
   * @returns 新的token信息
   */
  refreshToken: async (): Promise<ApiResponse<{ token: string }>> => {
    const response = await api.post('/auth/refresh');
    return response.data;
  },

  /**
   * 获取当前用户信息
   * @returns 当前登录用户信息
   */
  getCurrentUser: async (): Promise<ApiResponse<User>> => {
    const response = await api.get('/auth/me');
    return response.data;
  },

  /**
   * 修改密码
   * @param passwordData 密码修改信息
   * @returns 修改结果
   */
  changePassword: async (passwordData: { oldPassword: string; newPassword: string }): Promise<ApiResponse<void>> => {
    const response = await api.post('/auth/change-password', passwordData);
    return response.data;
  },
};

/**
 * 用户管理相关API
 */
export const userAPI = {
  /**
   * 获取用户列表
   * @param params 分页和搜索参数
   * @returns 用户列表和总数
   */
  getUsers: async (params: PaginationParams & SearchParams): Promise<ApiResponse<{ users: User[]; total: number }>> => {
    const response = await api.get('/users', { params });
    return response.data;
  },

  /**
   * 根据ID获取用户详情
   * @param userId 用户ID
   * @returns 用户详细信息
   */
  getUserById: async (userId: string): Promise<ApiResponse<User>> => {
    const response = await api.get(`/users/${userId}`);
    return response.data;
  },

  /**
   * 创建新用户
   * @param userData 用户信息
   * @returns 创建的用户信息
   */
  createUser: async (userData: Partial<User>): Promise<ApiResponse<User>> => {
    const response = await api.post('/users', userData);
    return response.data;
  },

  /**
   * 更新用户信息
   * @param userId 用户ID
   * @param userData 更新的用户信息
   * @returns 更新后的用户信息
   */
  updateUser: async (userId: string, userData: Partial<User>): Promise<ApiResponse<User>> => {
    const response = await api.put(`/users/${userId}`, userData);
    return response.data;
  },

  /**
   * 删除用户
   * @param userId 用户ID
   * @returns 删除结果
   */
  deleteUser: async (userId: string): Promise<ApiResponse<void>> => {
    const response = await api.delete(`/users/${userId}`);
    return response.data;
  },

  /**
   * 重置用户密码
   * @param userId 用户ID
   * @returns 重置结果
   */
  resetUserPassword: async (userId: string): Promise<ApiResponse<{ newPassword: string }>> => {
    const response = await api.post(`/users/${userId}/reset-password`);
    return response.data;
  },

  /**
   * 更新用户状态（启用/禁用）
   * @param userId 用户ID
   * @param status 新状态
   * @returns 更新结果
   */
  updateUserStatus: async (userId: string, status: 'active' | 'inactive'): Promise<ApiResponse<User>> => {
    const response = await api.patch(`/users/${userId}/status`, { status });
    return response.data;
  },
};

/**
 * 订单管理相关API
 */
export const orderAPI = {
  /**
   * 获取订单列表
   * @param params 分页、搜索和筛选参数
   * @returns 订单列表和总数
   */
  getOrders: async (params: PaginationParams & SearchParams): Promise<ApiResponse<{ orders: Order[]; total: number }>> => {
    const response = await api.get('/orders', { params });
    return response.data;
  },

  /**
   * 根据ID获取订单详情
   * @param orderId 订单ID
   * @returns 订单详细信息
   */
  getOrderById: async (orderId: string): Promise<ApiResponse<Order>> => {
    const response = await api.get(`/orders/${orderId}`);
    return response.data;
  },

  /**
   * 创建新订单
   * @param orderData 订单信息
   * @returns 创建的订单信息
   */
  createOrder: async (orderData: Partial<Order>): Promise<ApiResponse<Order>> => {
    const response = await api.post('/orders', orderData);
    return response.data;
  },

  /**
   * 更新订单信息
   * @param orderId 订单ID
   * @param orderData 更新的订单信息
   * @returns 更新后的订单信息
   */
  updateOrder: async (orderId: string, orderData: Partial<Order>): Promise<ApiResponse<Order>> => {
    const response = await api.put(`/orders/${orderId}`, orderData);
    return response.data;
  },

  /**
   * 删除订单
   * @param orderId 订单ID
   * @returns 删除结果
   */
  deleteOrder: async (orderId: string): Promise<ApiResponse<void>> => {
    const response = await api.delete(`/orders/${orderId}`);
    return response.data;
  },

  /**
   * 更新订单状态
   * @param orderId 订单ID
   * @param status 新状态
   * @returns 更新结果
   */
  updateOrderStatus: async (orderId: string, status: string): Promise<ApiResponse<Order>> => {
    const response = await api.patch(`/orders/${orderId}/status`, { status });
    return response.data;
  },

  /**
   * 批量更新订单状态
   * @param orderIds 订单ID数组
   * @param status 新状态
   * @returns 更新结果
   */
  batchUpdateOrderStatus: async (orderIds: string[], status: string): Promise<ApiResponse<{ updated: number }>> => {
    const response = await api.patch('/orders/batch-status', { orderIds, status });
    return response.data;
  },

  /**
   * 导出订单数据
   * @param params 导出参数
   * @returns 导出文件流
   */
  exportOrders: async (params: SearchParams): Promise<Blob> => {
    const response = await api.get('/orders/export', { 
      params, 
      responseType: 'blob' 
    });
    return response.data;
  },

  /**
   * 获取订单统计数据
   * @returns 订单统计信息
   */
  getOrderStats: async (): Promise<ApiResponse<{
    total: number;
    pending: number;
    processing: number;
    completed: number;
    cancelled: number;
    totalRevenue: number;
    monthlyRevenue: number;
  }>> => {
    const response = await api.get('/orders/stats');
    return response.data;
  },
};

/**
 * 黄金类型管理相关API
 */
export const goldTypeAPI = {
  /**
   * 获取黄金类型列表
   * @param params 分页和搜索参数
   * @returns 黄金类型列表和总数
   */
  getGoldTypes: async (params: PaginationParams & SearchParams): Promise<ApiResponse<{ goldTypes: GoldType[]; total: number }>> => {
    const response = await api.get('/gold-types', { params });
    return response.data;
  },

  /**
   * 根据ID获取黄金类型详情
   * @param goldTypeId 黄金类型ID
   * @returns 黄金类型详细信息
   */
  getGoldTypeById: async (goldTypeId: string): Promise<ApiResponse<GoldType>> => {
    const response = await api.get(`/gold-types/${goldTypeId}`);
    return response.data;
  },

  /**
   * 创建新黄金类型
   * @param goldTypeData 黄金类型信息
   * @returns 创建的黄金类型信息
   */
  createGoldType: async (goldTypeData: Partial<GoldType>): Promise<ApiResponse<GoldType>> => {
    const response = await api.post('/priceManual/addGoldPrice', goldTypeData);
    return response.data;
  },

  /**
   * 更新黄金类型信息
   * @param goldTypeId 黄金类型ID
   * @param goldTypeData 更新的黄金类型信息
   * @returns 更新后的黄金类型信息
   */
  updateGoldType: async (goldTypeId: string, goldTypeData: Partial<GoldType>): Promise<ApiResponse<GoldType>> => {
    const response = await api.put(`/gold-types/${goldTypeId}`, goldTypeData);
    return response.data;
  },

  /**
   * 删除黄金类型
   * @param goldTypeId 黄金类型ID
   * @returns 删除结果
   */
  deleteGoldType: async (goldTypeId: string): Promise<ApiResponse<void>> => {
    const response = await api.delete(`/gold-types/${goldTypeId}`);
    return response.data;
  },

  /**
   * 更新黄金价格
   * @param goldTypeId 黄金类型ID
   * @param price 新价格
   * @returns 更新结果
   */
  updateGoldPrice: async (goldTypeId: string, price: number): Promise<ApiResponse<GoldType>> => {
    const response = await api.patch(`/gold-types/${goldTypeId}/price`, { price });
    return response.data;
  },

  /**
   * 批量更新黄金价格
   * @param priceUpdates 价格更新数组
   * @returns 更新结果
   */
  batchUpdateGoldPrices: async (priceUpdates: Array<{ id: string; price: number }>): Promise<ApiResponse<{ updated: number }>> => {
    const response = await api.patch('/gold-types/batch-prices', { priceUpdates });
    return response.data;
  },
};

/**
 * 估价记录相关API
 */
export const quoteAPI = {
  /**
   * 获取估价记录列表
   * @param params 分页、搜索和筛选参数
   * @returns 估价记录列表和总数
   */
  getQuotes: async (params: PaginationParams & SearchParams): Promise<ApiResponse<{ quotes: Quote[]; total: number }>> => {
    const response = await api.get('/quotes', { params });
    return response.data;
  },

  /**
   * 根据ID获取估价记录详情
   * @param quoteId 估价记录ID
   * @returns 估价记录详细信息
   */
  getQuoteById: async (quoteId: string): Promise<ApiResponse<Quote>> => {
    const response = await api.get(`/quotes/${quoteId}`);
    return response.data;
  },

  /**
   * 创建新估价记录
   * @param quoteData 估价记录信息
   * @returns 创建的估价记录信息
   */
  createQuote: async (quoteData: Partial<Quote>): Promise<ApiResponse<Quote>> => {
    const response = await api.post('/quotes', quoteData);
    return response.data;
  },

  /**
   * 更新估价记录信息
   * @param quoteId 估价记录ID
   * @param quoteData 更新的估价记录信息
   * @returns 更新后的估价记录信息
   */
  updateQuote: async (quoteId: string, quoteData: Partial<Quote>): Promise<ApiResponse<Quote>> => {
    const response = await api.put(`/quotes/${quoteId}`, quoteData);
    return response.data;
  },

  /**
   * 删除估价记录
   * @param quoteId 估价记录ID
   * @returns 删除结果
   */
  deleteQuote: async (quoteId: string): Promise<ApiResponse<void>> => {
    const response = await api.delete(`/quotes/${quoteId}`);
    return response.data;
  },

  /**
   * 上传估价图片
   * @param quoteId 估价记录ID
   * @param imageFile 图片文件
   * @returns 上传结果
   */
  uploadQuoteImage: async (quoteId: string, imageFile: File): Promise<ApiResponse<{ imageUrl: string }>> => {
    const formData = new FormData();
    formData.append('image', imageFile);
    
    const response = await api.post(`/quotes/${quoteId}/images`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  },

  /**
   * 获取估价统计数据
   * @returns 估价统计信息
   */
  getQuoteStats: async (): Promise<ApiResponse<{
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    totalValue: number;
    avgValue: number;
  }>> => {
    const response = await api.get('/quotes/stats');
    return response.data;
  },
};

/**
 * 视频质检相关API
 */
export const videoInspectionAPI = {
  /**
   * 获取视频质检记录列表
   * @param params 分页、搜索和筛选参数
   * @returns 视频质检记录列表和总数
   */
  getVideoInspections: async (params: PaginationParams & SearchParams): Promise<ApiResponse<{ inspections: VideoInspection[]; total: number }>> => {
    const response = await api.get('/video-inspections', { params });
    return response.data;
  },

  /**
   * 根据ID获取视频质检记录详情
   * @param inspectionId 质检记录ID
   * @returns 视频质检记录详细信息
   */
  getVideoInspectionById: async (inspectionId: string): Promise<ApiResponse<VideoInspection>> => {
    const response = await api.get(`/video-inspections/${inspectionId}`);
    return response.data;
  },

  /**
   * 创建新视频质检记录
   * @param inspectionData 质检记录信息
   * @returns 创建的质检记录信息
   */
  createVideoInspection: async (inspectionData: Partial<VideoInspection>): Promise<ApiResponse<VideoInspection>> => {
    const response = await api.post('/video-inspections', inspectionData);
    return response.data;
  },

  /**
   * 更新视频质检记录信息
   * @param inspectionId 质检记录ID
   * @param inspectionData 更新的质检记录信息
   * @returns 更新后的质检记录信息
   */
  updateVideoInspection: async (inspectionId: string, inspectionData: Partial<VideoInspection>): Promise<ApiResponse<VideoInspection>> => {
    const response = await api.put(`/video-inspections/${inspectionId}`, inspectionData);
    return response.data;
  },

  /**
   * 删除视频质检记录
   * @param inspectionId 质检记录ID
   * @returns 删除结果
   */
  deleteVideoInspection: async (inspectionId: string): Promise<ApiResponse<void>> => {
    const response = await api.delete(`/video-inspections/${inspectionId}`);
    return response.data;
  },

  /**
   * 上传质检视频
   * @param inspectionId 质检记录ID
   * @param videoFile 视频文件
   * @returns 上传结果
   */
  uploadInspectionVideo: async (inspectionId: string, videoFile: File): Promise<ApiResponse<{ videoUrl: string }>> => {
    const formData = new FormData();
    formData.append('video', videoFile);
    
    const response = await api.post(`/video-inspections/${inspectionId}/video`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  },

  /**
   * 提交质检结果
   * @param inspectionId 质检记录ID
   * @param result 质检结果
   * @returns 提交结果
   */
  submitInspectionResult: async (inspectionId: string, result: {
    quality: 'excellent' | 'good' | 'fair' | 'poor';
    notes: string;
    approved: boolean;
  }): Promise<ApiResponse<VideoInspection>> => {
    const response = await api.post(`/video-inspections/${inspectionId}/result`, result);
    return response.data;
  },

  /**
   * 获取视频质检统计数据
   * @returns 质检统计信息
   */
  getVideoInspectionStats: async (): Promise<ApiResponse<{
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    approved: number;
    rejected: number;
  }>> => {
    const response = await api.get('/video-inspections/stats');
    return response.data;
  },
};

/**
 * 黄金价格相关API
 */
export const goldPriceAPI = {
  /**
   * 获取当前黄金价格
   * @returns 当前黄金价格信息
   */
  getCurrentPrice: async (): Promise<ApiResponse<GoldPrice>> => {
    const response = await api.get('/gold-prices/current');
    return response.data;
  },

  /**
   * 获取黄金价格历史数据
   * @param params 查询参数（时间范围、黄金类型等）
   * @returns 价格历史数据
   */
  getPriceHistory: async (params: {
    startDate: string;
    endDate: string;
    goldType?: string;
  }): Promise<ApiResponse<GoldPrice[]>> => {
    const response = await api.get('/gold-prices/history', { params });
    return response.data;
  },

  /**
   * 更新黄金价格
   * @param priceData 价格信息
   * @returns 更新结果
   */
  updatePrice: async (priceData: Partial<GoldPrice>): Promise<ApiResponse<GoldPrice>> => {
    const response = await api.post('/gold-prices', priceData);
    return response.data;
  },

  /**
   * 获取价格趋势分析
   * @param params 分析参数
   * @returns 价格趋势数据
   */
  getPriceTrends: async (params: {
    period: '1d' | '1w' | '1m' | '3m' | '1y';
    goldType?: string;
  }): Promise<ApiResponse<{
    trends: Array<{ date: string; price: number; change: number; changePercent: number }>;
    summary: { avgPrice: number; maxPrice: number; minPrice: number; volatility: number };
  }>> => {
    const response = await api.get('/gold-prices/trends', { params });
    return response.data;
  },
};

/**
 * 仪表盘相关API
 */
export const dashboardAPI = {
  /**
   * 获取仪表盘统计数据
   * @returns 仪表盘统计信息
   */
  getStats: async (): Promise<ApiResponse<DashboardStats>> => {
    const response = await api.get('/dashboard/stats');
    return response.data;
  },

  /**
   * 获取最近订单
   * @param limit 限制数量
   * @returns 最近订单列表
   */
  getRecentOrders: async (limit: number = 10): Promise<ApiResponse<Order[]>> => {
    const response = await api.get('/dashboard/recent-orders', { params: { limit } });
    return response.data;
  },

  /**
   * 获取系统概览数据
   * @returns 系统概览信息
   */
  getSystemOverview: async (): Promise<ApiResponse<{
    systemStatus: { name: string; status: string; lastCheck: string }[];
    performanceMetrics: { name: string; value: number; unit: string }[];
    recentActivities: Array<{ time: string; action: string; user: string; details: string }>;
  }>> => {
    const response = await api.get('/dashboard/system-overview');
    return response.data;
  },

  /**
   * 获取收入统计
   * @param params 统计参数
   * @returns 收入统计数据
   */
  getRevenueStats: async (params: {
    period: 'daily' | 'weekly' | 'monthly' | 'yearly';
    startDate?: string;
    endDate?: string;
  }): Promise<ApiResponse<{
    data: Array<{ date: string; revenue: number; orders: number }>;
    summary: { totalRevenue: number; totalOrders: number; avgOrderValue: number };
  }>> => {
    const response = await api.get('/dashboard/revenue-stats', { params });
    return response.data;
  },
};

/**
 * 系统设置相关API
 */
export const systemAPI = {
  /**
   * 获取系统配置
   * @returns 系统配置信息
   */
  getSystemConfig: async (): Promise<ApiResponse<{
    siteName: string;
    siteDescription: string;
    contactEmail: string;
    contactPhone: string;
    address: string;
    businessHours: string;
    maintenanceMode: boolean;
    allowRegistration: boolean;
    maxFileSize: number;
    sessionTimeout: number;
  }>> => {
    const response = await api.get('/system/config');
    return response.data;
  },

  /**
   * 更新系统配置
   * @param config 系统配置信息
   * @returns 更新结果
   */
  updateSystemConfig: async (config: any): Promise<ApiResponse<void>> => {
    const response = await api.put('/system/config', config);
    return response.data;
  },

  /**
   * 获取主题配置
   * @returns 主题配置信息
   */
  getThemeConfig: async (): Promise<ApiResponse<{
    primaryColor: string;
    borderRadius: number;
    compactMode: boolean;
    darkMode: boolean;
    fontSize: string;
  }>> => {
    const response = await api.get('/system/theme');
    return response.data;
  },

  /**
   * 更新主题配置
   * @param theme 主题配置信息
   * @returns 更新结果
   */
  updateThemeConfig: async (theme: any): Promise<ApiResponse<void>> => {
    const response = await api.put('/system/theme', theme);
    return response.data;
  },

  /**
   * 获取通知配置
   * @returns 通知配置信息
   */
  getNotificationConfig: async (): Promise<ApiResponse<{
    emailNotifications: boolean;
    smsNotifications: boolean;
    pushNotifications: boolean;
    orderUpdates: boolean;
    systemAlerts: boolean;
    marketingEmails: boolean;
  }>> => {
    const response = await api.get('/system/notifications');
    return response.data;
  },

  /**
   * 更新通知配置
   * @param notifications 通知配置信息
   * @returns 更新结果
   */
  updateNotificationConfig: async (notifications: any): Promise<ApiResponse<void>> => {
    const response = await api.put('/system/notifications', notifications);
    return response.data;
  },

  /**
   * 导出系统配置
   * @returns 配置文件
   */
  exportSystemConfig: async (): Promise<Blob> => {
    const response = await api.get('/system/config/export', {
      responseType: 'blob'
    });
    return response.data;
  },

  /**
   * 导入系统配置
   * @param configFile 配置文件
   * @returns 导入结果
   */
  importSystemConfig: async (configFile: File): Promise<ApiResponse<void>> => {
    const formData = new FormData();
    formData.append('config', configFile);
    
    const response = await api.post('/system/config/import', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  },

  /**
   * 重置系统配置
   * @returns 重置结果
   */
  resetSystemConfig: async (): Promise<ApiResponse<void>> => {
    const response = await api.post('/system/config/reset');
    return response.data;
  },
};

/**
 * 文件上传相关API
 */
export const fileAPI = {
  /**
   * 上传文件
   * @param file 文件对象
   * @param type 文件类型
   * @returns 上传结果
   */
  uploadFile: async (file: File, type: 'image' | 'video' | 'document'): Promise<ApiResponse<{
    fileId: string;
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
  }>> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);
    
    const response = await api.post('/files/upload', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return response.data;
  },

  /**
   * 删除文件
   * @param fileId 文件ID
   * @returns 删除结果
   */
  deleteFile: async (fileId: string): Promise<ApiResponse<void>> => {
    const response = await api.delete(`/files/${fileId}`);
    return response.data;
  },

  /**
   * 获取文件信息
   * @param fileId 文件ID
   * @returns 文件信息
   */
  getFileInfo: async (fileId: string): Promise<ApiResponse<{
    fileId: string;
    fileName: string;
    fileUrl: string;
    fileSize: number;
    mimeType: string;
    uploadTime: string;
    uploader: string;
  }>> => {
    const response = await api.get(`/files/${fileId}`);
    return response.data;
  },
};

export default api; 