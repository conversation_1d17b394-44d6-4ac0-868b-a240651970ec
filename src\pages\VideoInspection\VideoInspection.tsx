import React, { useState, useEffect, useRef } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Space, 
  Input, 
  Select, 
  DatePicker, 
  Tag, 
  Modal, 
  message,
  Tooltip,
  Row,
  Col,
  Statistic,
  Progress,
  Drawer,
  Form,
  Rate,
  InputNumber,
  Upload,
  List,
  Avatar,
  Divider,
  Alert,
  Popconfirm
} from 'antd';
import {
  SearchOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  EyeOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  VideoCameraOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  UploadOutlined,
  DownloadOutlined,
  FullscreenOutlined,
  SoundOutlined,
  AudioMutedOutlined,
  FileTextOutlined,
  UserOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { videoInspectionAPI } from '../../services/api';
import type { VideoInspection, SearchParams, PaginationParams } from '../../types';
import dayjs from 'dayjs';
import '../../styles/PageContainer.css';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

const VideoInspection: React.FC = () => {
  const [inspections, setInspections] = useState<VideoInspection[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [currentVideo, setCurrentVideo] = useState<VideoInspection | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setMuted] = useState(false);
  const [fullscreen, setFullscreen] = useState(false);
  const [inspectionModalVisible, setInspectionModalVisible] = useState(false);
  const [inspectionForm] = Form.useForm();
  const [currentInspectionId, setCurrentInspectionId] = useState<string>('');
  
  const videoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    loadInspections();
  }, [pagination.page, pagination.pageSize, searchParams]);

  const loadInspections = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...searchParams,
      };
      
      const response = await videoInspectionAPI.getVideoInspections(params);
      if (response.success) {
        setInspections(response.data.inspections);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
        }));
      }
    } catch (error) {
      console.error('加载视频质检记录失败:', error);
      message.error('加载视频质检记录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleReset = () => {
    setSearchParams({});
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setPagination(prev => ({
      ...prev,
      page: pagination.current,
      pageSize: pagination.pageSize,
    }));
  };

  const handleDelete = async (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条视频质检记录吗？此操作不可恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await videoInspectionAPI.deleteVideoInspection(id);
          if (response.success) {
            message.success('删除成功');
            loadInspections();
          } else {
            message.error(response.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  const handleExport = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要导出的记录');
      return;
    }
    message.info('导出功能开发中');
  };

  const handleRefresh = () => {
    loadInspections();
    message.success('数据已刷新');
  };

  const handlePreview = (record: VideoInspection) => {
    setCurrentVideo(record);
    setPreviewVisible(true);
  };

  const handlePreviewClose = () => {
    setPreviewVisible(false);
    setCurrentVideo(null);
    setIsPlaying(false);
    setCurrentTime(0);
  };

  const handlePlayPause = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleVolumeChange = (newVolume: number) => {
    if (videoRef.current) {
      videoRef.current.volume = newVolume;
      setVolume(newVolume);
      setMuted(newVolume === 0);
    }
  };

  const handleMuteToggle = () => {
    if (videoRef.current) {
      if (isMuted) {
        videoRef.current.volume = volume;
        setMuted(false);
      } else {
        videoRef.current.volume = 0;
        setMuted(true);
      }
    }
  };

  const handleFullscreen = () => {
    if (videoRef.current) {
      if (document.fullscreenElement) {
        document.exitFullscreen();
        setFullscreen(false);
      } else {
        videoRef.current.requestFullscreen();
        setFullscreen(true);
      }
    }
  };

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime);
    }
  };

  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      setDuration(videoRef.current.duration);
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newTime = parseFloat(e.target.value);
    if (videoRef.current) {
      videoRef.current.currentTime = newTime;
      setCurrentTime(newTime);
    }
  };

  const handleInspection = (record: VideoInspection) => {
    setCurrentInspectionId(record.id);
    inspectionForm.setFieldsValue({
      quality: 0,
      notes: record.inspectionNotes || '',
    });
    setInspectionModalVisible(true);
  };

  const handleInspectionSubmit = async () => {
    try {
      const values = await inspectionForm.validateFields();
      const response = await videoInspectionAPI.updateVideoInspection(currentInspectionId, {
        inspectionNotes: values.notes,
        status: 'completed',
        progress: 100,
      });
      if (response.success) {
        message.success('质检结果已保存');
        setInspectionModalVisible(false);
        loadInspections();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  // 计算统计数据
  const totalInspections = inspections.length;
  const completedInspections = inspections.filter(inspection => inspection.status === 'completed').length;
  const pendingInspections = inspections.filter(inspection => inspection.status === 'pending').length;
  const processingInspections = inspections.filter(inspection => inspection.status === 'processing').length;
  const avgQuality = inspections.length > 0 
    ? inspections.reduce((sum, inspection) => sum + (inspection.progress || 0), 0) / inspections.length 
    : 0;

  const columns = [
    {
      title: '质检ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff', fontWeight: 600 }}>
          {text}
        </span>
      ),
    },
    {
      title: '订单ID',
      dataIndex: 'orderId',
      key: 'orderId',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff', fontWeight: 600 }}>
          {text}
        </span>
      ),
    },
    {
      title: '视频时长',
      dataIndex: 'duration',
      key: 'duration',
      render: (text: number) => (
        <span style={{ fontWeight: 600 }}>
          {Math.floor(text / 60)}:{String(text % 60).padStart(2, '0')}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = 'default';
        let icon = null;
        if (status === 'completed') {
          color = 'success';
          icon = <CheckCircleOutlined />;
        } else if (status === 'processing') {
          color = 'processing';
          icon = <ClockCircleOutlined />;
        } else if (status === 'pending') {
          color = 'warning';
          icon = <ClockCircleOutlined />;
        }
        return (
          <Tag color={color} icon={icon}>
            {status === 'completed' ? '已完成' : status === 'processing' ? '处理中' : '待处理'}
          </Tag>
        );
      },
      filters: [
        { text: '待处理', value: 'pending' },
        { text: '处理中', value: 'processing' },
        { text: '已完成', value: 'completed' },
      ],
      onFilter: (value: boolean | React.Key, record: VideoInspection) => record.status === String(value),
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      render: (progress: number) => (
        <Progress percent={progress || 0} size="small" />
      ),
      sorter: (a: VideoInspection, b: VideoInspection) => (a.progress || 0) - (b.progress || 0),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <CalendarOutlined style={{ color: '#666' }} />
          {dayjs(text).format('YYYY-MM-DD HH:mm')}
        </div>
      ),
      sorter: (a: VideoInspection, b: VideoInspection) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: VideoInspection) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handlePreview(record)}
          >
            预览
          </Button>
          <Button 
            type="text" 
            icon={<VideoCameraOutlined />} 
            size="small"
            onClick={() => handleInspection(record)}
          >
            质检
          </Button>
          <Popconfirm
            title="确定要删除这条视频质检记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    },
  };

  return (
    <div className="page-container">
      <div className="page-content">
        {/* 页面头部 */}
        <div className="page-header">
          <h1>视频质检管理</h1>
          <p>管理黄金回收视频质检记录，支持视频预览、质量评估和结果记录</p>
        </div>

        {/* 统计信息 */}
        <div className="stats-grid">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总质检数"
                  value={totalInspections}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<VideoCameraOutlined />}
                  suffix="条"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="已完成"
                  value={completedInspections}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CheckCircleOutlined />}
                  suffix="条"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="待处理"
                  value={pendingInspections}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<ClockCircleOutlined />}
                  suffix="条"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="平均质量"
                  value={avgQuality}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<FileTextOutlined />}
                  suffix="分"
                  formatter={(value) => (typeof value === 'number' ? value.toFixed(1) : '0.0')}
                />
              </Card>
            </Col>
          </Row>
        </div>

        {/* 搜索和操作区域 */}
        <div className="search-section">
          <div className="search-row">
            <div className="search-item">
              <Input
                placeholder="搜索质检ID或客户姓名"
                prefix={<SearchOutlined />}
                value={searchParams.keyword || ''}
                onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
                allowClear
              />
            </div>
            <div className="search-item">
              <Select
                placeholder="选择状态"
                value={searchParams.status || 'all'}
                onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
                style={{ width: '100%' }}
              >
                <Option value="all">全部状态</Option>
                <Option value="待处理">待处理</Option>
                <Option value="处理中">处理中</Option>
                <Option value="已完成">已完成</Option>
              </Select>
            </div>
            <div className="search-item">
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                value={searchParams.dateRange ? [dayjs(searchParams.dateRange[0]), dayjs(searchParams.dateRange[1])] : null}
                onChange={(dates) => {
                  if (dates && dates[0] && dates[1]) {
                    setSearchParams(prev => ({ 
                      ...prev, 
                      dateRange: [dates[0]!.toISOString(), dates[1]!.toISOString()]
                    }));
                  } else {
                    setSearchParams(prev => ({ 
                      ...prev, 
                      dateRange: undefined
                    }));
                  }
                }}
                style={{ width: '100%' }}
              />
            </div>
          </div>
          <div className="search-actions">
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              搜索
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>
              重置
            </Button>
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="action-section">
          <div className="action-buttons">
            <Button icon={<ExportOutlined />} onClick={handleExport} disabled={selectedRowKeys.length === 0}>
              导出数据
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
              刷新数据
            </Button>
          </div>
          <div className="selected-info">
            已选择 {selectedRowKeys.length} 项
          </div>
        </div>

        {/* 表格区域 */}
        <div className="table-section">
          <Card>
            <Table
              columns={columns}
              dataSource={inspections}
              rowKey="id"
              loading={loading}
              rowSelection={rowSelection}
              pagination={{
                current: pagination.page,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, page, pageSize: pageSize || 10 }));
                },
              }}
              onChange={handleTableChange}
              scroll={{ x: 1200 }}
            />
          </Card>
        </div>

        {/* 视频预览弹窗 */}
        <Modal
          title="视频预览"
          open={previewVisible}
          onCancel={handlePreviewClose}
          width={800}
          footer={null}
          destroyOnClose
        >
          {currentVideo && (
            <div style={{ textAlign: 'center' }}>
              <video
                ref={videoRef}
                src={currentVideo.videoUrl}
                style={{ width: '100%', maxHeight: '400px' }}
                onTimeUpdate={handleTimeUpdate}
                onLoadedMetadata={handleLoadedMetadata}
                onEnded={() => setIsPlaying(false)}
              />
              
              <div style={{ marginTop: '16px' }}>
                <Space size="large">
                  <Button 
                    type="primary" 
                    icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                    onClick={handlePlayPause}
                  >
                    {isPlaying ? '暂停' : '播放'}
                  </Button>
                  
                  <Button 
                    icon={isMuted ? <AudioMutedOutlined /> : <SoundOutlined />}
                    onClick={handleMuteToggle}
                  >
                    {isMuted ? '取消静音' : '静音'}
                  </Button>
                  
                  <Button 
                    icon={<FullscreenOutlined />}
                    onClick={handleFullscreen}
                  >
                    全屏
                  </Button>
                </Space>
              </div>
              
              <div style={{ marginTop: '16px' }}>
                <input
                  type="range"
                  min="0"
                  max={duration}
                  value={currentTime}
                  onChange={handleSeek}
                  style={{ width: '100%' }}
                />
                <div style={{ display: 'flex', justifyContent: 'space-between', marginTop: '8px' }}>
                  <span>{Math.floor(currentTime / 60)}:{String(Math.floor(currentTime % 60)).padStart(2, '0')}</span>
                  <span>{Math.floor(duration / 60)}:{String(Math.floor(duration % 60)).padStart(2, '0')}</span>
                </div>
              </div>
              
              <div style={{ marginTop: '16px' }}>
                <span>音量: </span>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={volume}
                  onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                  style={{ width: '100px' }}
                />
              </div>
            </div>
          )}
        </Modal>

        {/* 质检结果弹窗 */}
        <Modal
          title="质检结果"
          open={inspectionModalVisible}
          onOk={handleInspectionSubmit}
          onCancel={() => setInspectionModalVisible(false)}
          okText="保存"
          cancelText="取消"
        >
          <Form form={inspectionForm} layout="vertical">
            <Form.Item
              name="quality"
              label="质量评分"
              rules={[{ required: true, message: '请选择质量评分' }]}
            >
              <Rate />
            </Form.Item>
            <Form.Item
              name="notes"
              label="质检备注"
            >
              <TextArea rows={4} placeholder="请输入质检备注" />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    </div>
  );
};

export default VideoInspection; 