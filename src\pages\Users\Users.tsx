import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Space, 
  Input, 
  Select, 
  DatePicker, 
  Tag, 
  Modal, 
  message,
  Tooltip,
  Row,
  Col,
  Statistic,
  Form,
  Switch,
  Avatar,
  Drawer,
  Divider,
  Alert,
  Popconfirm,
  Descriptions
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  UserOutlined,
  LockOutlined,
  UnlockOutlined,
  EyeOutlined,
  KeyOutlined,
  TeamOutlined,
  SafetyOutlined,
  CalendarOutlined,
  PhoneOutlined,
  EnvironmentOutlined
} from '@ant-design/icons';
import { userAPI } from '../../services/api';
import type { User, SearchParams, PaginationParams } from '../../types';
import dayjs from 'dayjs';
import '../../styles/PageContainer.css';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Password } = Input;

const Users: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [userModalVisible, setUserModalVisible] = useState(false);
  const [userDrawerVisible, setUserDrawerVisible] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [userForm] = Form.useForm();
  const [passwordForm] = Form.useForm();

  useEffect(() => {
    loadUsers();
  }, [pagination.page, pagination.pageSize, searchParams]);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...searchParams,
      };
      
      const response = await userAPI.getUsers(params);
      if (response.success) {
        setUsers(response.data.users);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
        }));
      }
    } catch (error) {
      console.error('加载用户列表失败:', error);
      message.error('加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleReset = () => {
    setSearchParams({});
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setPagination(prev => ({
      ...prev,
      page: pagination.current,
      pageSize: pagination.pageSize,
    }));
  };

  const handleAddUser = () => {
    setIsEdit(false);
    setCurrentUser(null);
    userForm.resetFields();
    setUserModalVisible(true);
  };

  const handleEditUser = (user: User) => {
    setIsEdit(true);
    setCurrentUser(user);
    userForm.setFieldsValue({
      username: user.username,
      phone: user.phone,
      address: user.address,
    });
    setUserModalVisible(true);
  };

  const handleViewUser = (user: User) => {
    setCurrentUser(user);
    setUserDrawerVisible(true);
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      const response = await userAPI.deleteUser(userId);
      if (response.success) {
        message.success('用户删除成功');
        loadUsers();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除用户失败:', error);
      message.error('删除失败');
    }
  };

  const handleUserModalOk = async () => {
    try {
      const values = await userForm.validateFields();
      if (isEdit && currentUser) {
        const response = await userAPI.updateUser(currentUser.id, values);
        if (response.success) {
          message.success('用户信息更新成功');
          setUserModalVisible(false);
          loadUsers();
        } else {
          message.error(response.message || '更新失败');
        }
      } else {
        const response = await userAPI.createUser(values);
        if (response.success) {
          message.success('用户创建成功');
          setUserModalVisible(false);
          loadUsers();
        } else {
          message.error(response.message || '创建失败');
        }
      }
    } catch (error) {
      console.error('保存用户信息失败:', error);
      message.error('保存失败');
    }
  };

  const handleUserModalCancel = () => {
    setUserModalVisible(false);
    userForm.resetFields();
  };

  const handleExport = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要导出的用户');
      return;
    }
    message.info('导出功能开发中');
  };

  const handleRefresh = () => {
    loadUsers();
    message.success('数据已刷新');
  };

  const handleResetPassword = async (userId: string) => {
    try {
      const response = await userAPI.resetPassword(userId);
      if (response.success) {
        message.success('密码重置成功');
      } else {
        message.error(response.message || '重置失败');
      }
    } catch (error) {
      console.error('重置密码失败:', error);
      message.error('重置失败');
    }
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes((searchParams.keyword || '').toLowerCase()) ||
                         user.phone.includes(searchParams.keyword || '');
    const matchesStatus = !searchParams.status || searchParams.status === 'all';
    const matchesDate = !searchParams.dateRange || (
      new Date(user.createdAt) >= new Date(searchParams.dateRange[0]) &&
      new Date(user.createdAt) <= new Date(searchParams.dateRange[1])
    );
    return matchesSearch && matchesStatus && matchesDate;
  });

  // 计算统计数据
  const totalUsers = users.length;
  const activeUsers = users.filter(user => user.status === 'active').length;
  const inactiveUsers = users.filter(user => user.status === 'inactive').length;
  const newUsersThisMonth = users.filter(user => {
    const userDate = new Date(user.createdAt);
    const now = new Date();
    return userDate.getMonth() === now.getMonth() && userDate.getFullYear() === now.getFullYear();
  }).length;

  const columns = [
    {
      title: '用户ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff', fontWeight: 600 }}>
          {text}
        </span>
      ),
    },
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <Avatar icon={<UserOutlined />} size="small" />
          <span style={{ fontWeight: 600 }}>{text}</span>
        </div>
      ),
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <PhoneOutlined style={{ color: '#666' }} />
          {text}
        </div>
      ),
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <EnvironmentOutlined style={{ color: '#666' }} />
          <span style={{ maxWidth: '200px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
            {text}
          </span>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'active' ? 'success' : 'default'}>
          {status === 'active' ? '活跃' : '非活跃'}
        </Tag>
      ),
      filters: [
        { text: '全部', value: 'all' },
        { text: '活跃', value: 'active' },
        { text: '非活跃', value: 'inactive' },
      ],
      onFilter: (value: string, record: User) => value === 'all' || record.status === value,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <CalendarOutlined style={{ color: '#666' }} />
          {dayjs(text).format('YYYY-MM-DD')}
        </div>
      ),
      sorter: (a: User, b: User) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: User) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewUser(record)}
          >
            查看
          </Button>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditUser(record)}
          >
            编辑
          </Button>
          <Button 
            type="text" 
            icon={<KeyOutlined />} 
            size="small"
            onClick={() => handleResetPassword(record.id)}
          >
            重置密码
          </Button>
          <Popconfirm
            title="确定要删除这个用户吗？"
            onConfirm={() => handleDeleteUser(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    },
  };

  return (
    <div className="page-container">
      <div className="page-content">
        {/* 页面头部 */}
        <div className="page-header">
          <h1>用户管理</h1>
          <p>管理系统用户信息，包括用户创建、编辑、权限管理和状态控制</p>
        </div>

        {/* 统计信息 */}
        <div className="stats-grid">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总用户数"
                  value={totalUsers}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<TeamOutlined />}
                  suffix="人"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="活跃用户"
                  value={activeUsers}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<UserOutlined />}
                  suffix="人"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="非活跃用户"
                  value={inactiveUsers}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<UserOutlined />}
                  suffix="人"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="本月新增"
                  value={newUsersThisMonth}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<PlusOutlined />}
                  suffix="人"
                />
              </Card>
            </Col>
          </Row>
        </div>

        {/* 搜索和操作区域 */}
        <div className="search-section">
          <div className="search-row">
            <div className="search-item">
              <Input
                placeholder="搜索用户名或手机号"
                prefix={<SearchOutlined />}
                value={searchParams.keyword || ''}
                onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
                allowClear
              />
            </div>
            <div className="search-item">
              <Select
                placeholder="选择状态"
                value={searchParams.status || 'all'}
                onChange={(value) => setSearchParams(prev => ({ ...prev, status: value }))}
                style={{ width: '100%' }}
              >
                <Option value="all">全部状态</Option>
                <Option value="active">活跃</Option>
                <Option value="inactive">非活跃</Option>
              </Select>
            </div>
            <div className="search-item">
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                value={searchParams.dateRange ? [dayjs(searchParams.dateRange[0]), dayjs(searchParams.dateRange[1])] : null}
                onChange={(dates) => {
                  if (dates && dates[0] && dates[1]) {
                    setSearchParams(prev => ({ 
                      ...prev, 
                      dateRange: [dates[0].toISOString(), dates[1].toISOString()]
                    }));
                  } else {
                    setSearchParams(prev => ({ 
                      ...prev, 
                      dateRange: undefined
                    }));
                  }
                }}
                style={{ width: '100%' }}
              />
            </div>
          </div>
          <div className="search-actions">
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              搜索
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>
              重置
            </Button>
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="action-section">
          <div className="action-buttons">
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddUser}>
              新增用户
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExport} disabled={selectedRowKeys.length === 0}>
              导出数据
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
              刷新数据
            </Button>
          </div>
          <div className="selected-info">
            已选择 {selectedRowKeys.length} 项
          </div>
        </div>

        {/* 表格区域 */}
        <div className="table-section">
          <Card>
            <Table
              columns={columns}
              dataSource={filteredUsers}
              rowKey="id"
              loading={loading}
              rowSelection={rowSelection}
              pagination={{
                current: pagination.page,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, page, pageSize: pageSize || 10 }));
                },
              }}
              onChange={handleTableChange}
              scroll={{ x: 1200 }}
            />
          </Card>
        </div>

        {/* 新增/编辑用户弹窗 */}
        <Modal
          title={isEdit ? '编辑用户' : '新增用户'}
          open={userModalVisible}
          onOk={handleUserModalOk}
          onCancel={handleUserModalCancel}
          width={600}
          okText="确定"
          cancelText="取消"
        >
          <Form form={userForm} layout="vertical">
            <Form.Item
              name="username"
              label="用户名"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input prefix={<UserOutlined />} placeholder="请输入用户名" />
            </Form.Item>
            <Form.Item
              name="phone"
              label="联系电话"
              rules={[{ required: true, message: '请输入联系电话' }]}
            >
              <Input prefix={<PhoneOutlined />} placeholder="请输入联系电话" />
            </Form.Item>
            <Form.Item
              name="address"
              label="地址"
              rules={[{ required: true, message: '请输入地址' }]}
            >
              <Input prefix={<EnvironmentOutlined />} placeholder="请输入地址" />
            </Form.Item>
            {!isEdit && (
              <Form.Item
                name="password"
                label="密码"
                rules={[{ required: true, message: '请输入密码' }]}
              >
                <Password prefix={<LockOutlined />} placeholder="请输入密码" />
              </Form.Item>
            )}
          </Form>
        </Modal>

        {/* 用户详情抽屉 */}
        <Drawer
          title="用户详情"
          placement="right"
          width={600}
          open={userDrawerVisible}
          onClose={() => setUserDrawerVisible(false)}
        >
          {currentUser && (
            <div>
              <div style={{ textAlign: 'center', marginBottom: '24px' }}>
                <Avatar 
                  size={80} 
                  icon={<UserOutlined />}
                  style={{ backgroundColor: '#1890ff', marginBottom: '16px' }}
                />
                <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px' }}>
                  {currentUser.username}
                </div>
                <Tag color={currentUser.status === 'active' ? 'success' : 'default'}>
                  {currentUser.status === 'active' ? '活跃' : '非活跃'}
                </Tag>
              </div>
              
              <Descriptions title="基本信息" bordered column={1}>
                <Descriptions.Item label="用户ID">
                  <span style={{ fontFamily: 'monospace', color: '#1890ff' }}>
                    {currentUser.id}
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="用户名">
                  {currentUser.username}
                </Descriptions.Item>
                <Descriptions.Item label="联系电话">
                  {currentUser.phone}
                </Descriptions.Item>
                <Descriptions.Item label="地址">
                  {currentUser.address}
                </Descriptions.Item>
              </Descriptions>
              
              <Divider />
              
              <Descriptions title="账户信息" bordered column={1}>
                <Descriptions.Item label="创建时间">
                  {dayjs(currentUser.createdAt).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {dayjs(currentUser.updatedAt).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
              </Descriptions>
            </div>
          )}
        </Drawer>
      </div>
    </div>
  );
};

export default Users; 