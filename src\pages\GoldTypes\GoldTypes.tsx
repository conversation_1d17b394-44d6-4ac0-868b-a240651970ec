import React, { useState } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  Select, 
  Modal, 
  Form, 
  InputNumber, 
  Popconfirm, 
  Row, 
  Col,
  Drawer,
  Descriptions,
  Divider,
  Typography,
  Statistic,
  Switch,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  GoldOutlined,
  DollarOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { useAppStore } from '../../store';
import '../../styles/PageContainer.css';

const { Option } = Select;
const { Text } = Typography;

const GoldTypes: React.FC = () => {
  const { goldTypes, setGoldTypes } = useAppStore();
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [editingGoldType, setEditingGoldType] = useState<any>(null);
  const [selectedGoldType, setSelectedGoldType] = useState<any>(null);
  const [form] = Form.useForm();

  // 计算统计数据
  const totalTypes = goldTypes.length;
  const activeTypes = goldTypes.filter(type => type.isActive).length;
  const inactiveTypes = goldTypes.filter(type => !type.isActive).length;
  const totalValue = goldTypes.reduce((sum, type) => sum + (type.unitPrice || 0), 0);
  const avgPrice = totalTypes > 0 ? totalValue / totalTypes : 0;

  const handleAddGoldType = () => {
    setEditingGoldType(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  
  const handleEditGoldType = (record: any) => {
    setEditingGoldType(record);
    form.setFieldsValue({
      name: record.name,
      code: record.code,
      purity: record.purity,
      unitPrice: record.unitPrice,
      description: record.description,
      category: record.category,
      isActive: record.isActive,
    });
    setIsModalVisible(true);
  };

  const handleViewGoldType = (record: any) => {
    setSelectedGoldType(record);
    setIsDetailVisible(true);
  };

  const handleDeleteGoldType = (goldTypeId: string) => {
    const updatedGoldTypes = goldTypes.filter(type => type.id !== goldTypeId);
    setGoldTypes(updatedGoldTypes);
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (editingGoldType) {
        // 编辑黄金类型
        const updatedGoldTypes = goldTypes.map(type => 
          type.id === editingGoldType.id 
            ? { ...type, ...values, updatedAt: new Date().toISOString() }
            : type
        );
        setGoldTypes(updatedGoldTypes);
      } else {
        // 新增黄金类型
        const newGoldType = {
          id: `GT${Date.now()}`,
          code: `GT${Date.now()}`,
          ...values,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        console.log(newGoldType);
        
        setGoldTypes([...goldTypes, newGoldType]);
      }
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleSearch = () => {
    // 搜索逻辑
  };

  const handleReset = () => {
    setSearchText('');
    setCategoryFilter('all');
    setStatusFilter('all');
  };

  const handleExport = () => {
    if (filteredGoldTypes.length === 0) {
      message.warning('没有数据可以导出');
      return;
    }

    try {
      // 准备导出数据
      const exportData = filteredGoldTypes.map(type => ({
        '类型代码': type.code,
        '类型名称': type.name,
        '纯度(%)': type.purity,
        '类别': type.category,
        '单价(¥/g)': type.unitPrice,
        '状态': type.isActive ? '启用' : '禁用',
        '描述': type.description || '',
        '创建时间': type.createdAt,
        '更新时间': type.updatedAt,
      }));

      // 创建CSV内容
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => 
          headers.map(header => {
            const value = row[header];
            // 处理包含逗号的值
            if (typeof value === 'string' && value.includes(',')) {
              return `"${value}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      // 创建下载链接
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `黄金类型数据_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success(`成功导出 ${filteredGoldTypes.length} 条数据`);
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    }
  };

  const handleRefresh = () => {
    // 刷新逻辑
  };

  const handleStatusToggle = (goldTypeId: string, isActive: boolean) => {
    const updatedGoldTypes = goldTypes.map(type => 
      type.id === goldTypeId 
        ? { ...type, isActive, updatedAt: new Date().toISOString() }
        : type
    );
    setGoldTypes(updatedGoldTypes);
  };

  const filteredGoldTypes = goldTypes.filter(type => {
    const matchesSearch = type.name.toLowerCase().includes(searchText.toLowerCase()) ||
                         type.code.toLowerCase().includes(searchText.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || type.category === categoryFilter;
    const matchesStatus = statusFilter === 'all' || 
                         (statusFilter === 'active' && type.isActive) ||
                         (statusFilter === 'inactive' && !type.isActive);
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const columns = [
    {
      title: '类型代码',
      dataIndex: 'code',
      key: 'code',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff', fontWeight: 600 }}>
          {text}
        </span>
      ),
    },
    {
      title: '类型名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <GoldOutlined style={{ color: '#faad14' }} />
          {text}
        </div>
      ),
    },
    {
      title: '纯度(%)',
      dataIndex: 'purity',
      key: 'purity',
      render: (text: number) => (
        <span style={{ fontWeight: 600, color: '#722ed1' }}>{text}%</span>
      ),
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (text: string) => (
        <Tag color="blue">{text}</Tag>
      ),
      filters: [
        { text: '足金', value: '足金' },
        { text: 'K金', value: 'K金' },
        { text: '金条', value: '金条' },
        { text: '其他', value: '其他' },
      ],
      onFilter: (value: string, record: any) => record.category === value,
    },
    {
      title: '单价(¥/g)',
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      render: (text: number) => (
        <span style={{ color: '#52c41a', fontWeight: 600 }}>
          ¥{text?.toLocaleString()}
        </span>
      ),
      sorter: (a: any, b: any) => (a.unitPrice || 0) - (b.unitPrice || 0),
    },
    {
      title: '状态',
      dataIndex: 'isActive',
      key: 'isActive',
      render: (isActive: boolean) => (
        <Tag color={isActive ? 'success' : 'default'} icon={isActive ? <CheckCircleOutlined /> : <ClockCircleOutlined />}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
      filters: [
        { text: '启用', value: 'active' },
        { text: '禁用', value: 'inactive' },
      ],
      onFilter: (value: string, record: any) => 
        (value === 'active' && record.isActive) || (value === 'inactive' && !record.isActive),
    },
    {
      title: '最后更新',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ClockCircleOutlined style={{ color: '#666' }} />
          {new Date(text).toLocaleDateString()}
        </div>
      ),
      sorter: (a: any, b: any) => new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewGoldType(record)}
          >
            查看
          </Button>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditGoldType(record)}
          >
            编辑
          </Button>
          <Switch
            checked={record.isActive}
            onChange={(checked) => handleStatusToggle(record.id, checked)}
            size="small"
          />
          <Popconfirm
            title="确定要删除这个黄金类型吗？"
            onConfirm={() => handleDeleteGoldType(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="page-container">
      <div className="page-content">
        {/* 页面头部 */}
        <div className="page-header">
          <h1>黄金类型管理</h1>
          <p>管理黄金类型、纯度、价格等信息，支持价格更新和状态管理</p>
        </div>

        {/* 统计信息 */}
        <div className="stats-grid">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总类型数"
                  value={totalTypes}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<GoldOutlined />}
                  suffix="种"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="启用类型"
                  value={activeTypes}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CheckCircleOutlined />}
                  suffix="种"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="禁用类型"
                  value={inactiveTypes}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<ExclamationCircleOutlined />}
                  suffix="种"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="平均单价"
                  value={avgPrice}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<DollarOutlined />}
                  suffix="¥/g"
                  formatter={(value) => value.toFixed(2)}
                />
              </Card>
            </Col>
          </Row>
        </div>

        {/* 搜索和操作区域 */}
        <div className="search-section">
          <div className="search-row">
            <div className="search-item">
              <Input
                placeholder="搜索类型名称或代码"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </div>
            <div className="search-item">
              <Select
                placeholder="选择类别"
                value={categoryFilter}
                onChange={setCategoryFilter}
                style={{ width: '100%' }}
              >
                <Option value="all">全部类别</Option>
                <Option value="足金">足金</Option>
                <Option value="K金">K金</Option>
                <Option value="金条">金条</Option>
                <Option value="其他">其他</Option>
              </Select>
            </div>
            <div className="search-item">
              <Select
                placeholder="选择状态"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: '100%' }}
              >
                <Option value="all">全部状态</Option>
                <Option value="active">启用</Option>
                <Option value="inactive">禁用</Option>
              </Select>
            </div>
          </div>
          <div className="search-actions">
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              搜索
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>
              重置
            </Button>
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="action-section">
          <div className="action-buttons">
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddGoldType}>
              新增类型
            </Button>
            <Button 
              icon={<DownloadOutlined />} 
              onClick={handleExport}
              disabled={filteredGoldTypes.length === 0}
              title={filteredGoldTypes.length === 0 ? '没有数据可以导出' : '导出当前筛选结果'}
            >
              导出数据
            </Button>
            <Button icon={<SyncOutlined />} onClick={handleRefresh}>
              刷新数据
            </Button>
          </div>
          <div className="selected-info">
            共找到 {filteredGoldTypes.length} 条记录
          </div>
        </div>

        {/* 表格区域 */}
        <div className="table-section">
          <Card>
            <Table
              columns={columns}
              dataSource={filteredGoldTypes}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              scroll={{ x: 1200 }}
            />
          </Card>
        </div>

        {/* 新增/编辑黄金类型弹窗 */}
        <Modal
          title={editingGoldType ? '编辑黄金类型' : '新增黄金类型'}
          open={isModalVisible}
          onOk={handleModalOk}
          onCancel={handleModalCancel}
          width={800}
          okText="确定"
          cancelText="取消"
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              name: '',
              code: '',
              purity: 99.9,
              unitPrice: 0,
              description: '',
              category: '足金',
              isActive: true,
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="类型名称"
                  rules={[{ required: true, message: '请输入类型名称' }]}
                >
                  <Input prefix={<GoldOutlined />} placeholder="请输入类型名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="code"
                  label="类型代码"
                  rules={[{ required: true, message: '请输入类型代码' }]}
                >
                  <Input prefix={<GoldOutlined />} placeholder="请输入类型代码" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="purity"
                  label="纯度(%)"
                  rules={[{ required: true, message: '请输入纯度' }]}
                >
                  <InputNumber
                    prefix={<GoldOutlined />}
                    placeholder="请输入纯度"
                    min={0}
                    max={100}
                    step={0.1}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="unitPrice"
                  label="单价(¥/g)"
                  rules={[{ required: true, message: '请输入单价' }]}
                >
                  <InputNumber
                    prefix={<DollarOutlined />}
                    placeholder="请输入单价"
                    min={0}
                    step={0.01}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="category"
                  label="类别"
                  rules={[{ required: true, message: '请选择类别' }]}
                >
                  <Select placeholder="请选择类别">
                    <Option value="足金">足金</Option>
                    <Option value="K金">K金</Option>
                    <Option value="金条">金条</Option>
                    <Option value="其他">其他</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="isActive"
                  label="状态"
                  valuePropName="checked"
                >
                  <Switch checkedChildren="启用" unCheckedChildren="禁用" />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="description"
              label="描述"
            >
              <Input.TextArea rows={3} placeholder="请输入描述信息" />
            </Form.Item>
          </Form>
        </Modal>

        {/* 黄金类型详情抽屉 */}
        <Drawer
          title="黄金类型详情"
          placement="right"
          width={600}
          open={isDetailVisible}
          onClose={() => setIsDetailVisible(false)}
        >
          {selectedGoldType && (
            <div>
              <Descriptions title="基本信息" bordered column={1}>
                <Descriptions.Item label="类型代码">
                  <Text code>{selectedGoldType.code}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="类型名称">
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <GoldOutlined style={{ color: '#faad14' }} />
                    {selectedGoldType.name}
                  </div>
                </Descriptions.Item>
                <Descriptions.Item label="类别">
                  <Tag color="blue">{selectedGoldType.category}</Tag>
                </Descriptions.Item>
              </Descriptions>
              
              <Divider />
              
              <Descriptions title="规格信息" bordered column={1}>
                <Descriptions.Item label="纯度">
                  <span style={{ fontWeight: 600, color: '#722ed1' }}>
                    {selectedGoldType.purity}%
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="单价">
                  <span style={{ color: '#52c41a', fontWeight: 600 }}>
                    ¥{selectedGoldType.unitPrice?.toLocaleString()}/g
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={selectedGoldType.isActive ? 'success' : 'default'}>
                    {selectedGoldType.isActive ? '启用' : '禁用'}
                  </Tag>
                </Descriptions.Item>
              </Descriptions>
              
              <Divider />
              
              <Descriptions title="其他信息" bordered column={1}>
                <Descriptions.Item label="创建时间">
                  {new Date(selectedGoldType.createdAt).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {new Date(selectedGoldType.updatedAt).toLocaleString()}
                </Descriptions.Item>
                {selectedGoldType.description && (
                  <Descriptions.Item label="描述">
                    {selectedGoldType.description}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </div>
          )}
        </Drawer>
      </div>
    </div>
  );
};

export default GoldTypes; 