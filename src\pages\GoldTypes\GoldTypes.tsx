import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Select,
  Modal,
  Form,
  InputNumber,
  Popconfirm,
  Row,
  Col,
  Drawer,
  Descriptions,
  Divider,
  Typography,
  Statistic,
  Switch,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  GoldOutlined,
  DollarOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ReloadOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { goldTypeAPI } from '../../services/api';
import type { GoldType } from '../../types';
import '../../styles/PageContainer.css';

const { Option } = Select;
const { Text } = Typography;

const GoldTypes: React.FC = () => {
  const [goldTypes, setGoldTypes] = useState<GoldType[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [editingGoldType, setEditingGoldType] = useState<GoldType | null>(null);
  const [selectedGoldType, setSelectedGoldType] = useState<GoldType | null>(null);
  const [form] = Form.useForm();

  // 计算统计数据
  const totalTypes = goldTypes.length;
  const activeTypes = goldTypes.filter(type => type.status === 1).length;
  const inactiveTypes = goldTypes.filter(type => type.status === 0).length;
  const totalValue = goldTypes.reduce((sum, type) => sum + (type.pricePerG || 0), 0);
  const avgPrice = totalTypes > 0 ? totalValue / totalTypes : 0;

  // 加载黄金类型数据
  const loadGoldTypes = async (purityCode: string = '') => {
    try {
      setLoading(true);
      if (purityCode) {
        // 如果有搜索条件，使用原来的接口
        const response = await goldTypeAPI.getGoldTypes({ purityCode });
        if (response.code==200) {
          setGoldTypes(response.data);
        } else {
          message.error(response.message || '获取数据失败');
        }
      } else {
        // 如果没有搜索条件，使用新的获取全部数据的接口
        const response = await goldTypeAPI.getAllGoldPrice({ pageNumber: 1, rowsPerPage: 1000 });
        if (response.code==200) {
          setGoldTypes(response.data.content);
        } else {
          message.error(response.message || '获取数据失败');
        }
      }
    } catch (error) {
      console.error('获取黄金类型失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时加载数据
  useEffect(() => {
    loadGoldTypes();
  }, []);

  const handleAddGoldType = () => {
    setEditingGoldType(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditGoldType = (record: GoldType) => {
    setEditingGoldType(record);
    form.setFieldsValue({
      purityCode: record.purityCode,
      itemCategory: record.itemCategory,
      pricePerG: record.pricePerG,
      status: record.status,
      effectiveFrom: record.effectiveFrom ? record.effectiveFrom.split('T')[0] : '',
      remark: record.remark,
    });
    setIsModalVisible(true);
  };

  const handleViewGoldType = (record: GoldType) => {
    setSelectedGoldType(record);
    setIsDetailVisible(true);
  };

  const handleDeleteGoldType = async (goldTypeId: number) => {
    try {
      const response = await goldTypeAPI.deleteGoldType({ id: goldTypeId });
      if (response.success) {
        message.success('删除成功');
        loadGoldTypes(); // 重新加载数据
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      if (editingGoldType) {
        // 编辑黄金类型
        const updateData = {
          id: editingGoldType.id,
          ...values
        };
        const response = await goldTypeAPI.updateGoldType(updateData);
        if (response.code === 200) {
          message.success('更新成功');
          loadGoldTypes(); // 重新加载数据
        } else {
          message.error(response.message || '更新失败');
        }
      } else {
        // 新增黄金类型
        const response = await goldTypeAPI.createGoldType(values);
        if (response.success) {
          message.success('添加成功');
          loadGoldTypes(); // 重新加载数据
        } else {
          message.error(response.message || '添加失败');
        }
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('操作失败:', error);
      message.error('操作失败');
    }
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleSearch = () => {
    loadGoldTypes(searchText);
  };

  const handleReset = () => {
    setSearchText('');
    setCategoryFilter('all');
    setStatusFilter('all');
    loadGoldTypes();
  };

  const handleExport = () => {
    if (filteredGoldTypes.length === 0) {
      message.warning('没有数据可以导出');
      return;
    }

    try {
      // 准备导出数据
      const exportData = filteredGoldTypes.map(type => ({
        '成色编码': type.purityCode,
        '类别': type.itemCategory,
        '参考价(¥/g)': type.pricePerG,
        '状态': type.status === 1 ? '启用' : '停用',
        '生效时间': type.effectiveFrom,
        '备注': type.remark || '',
      }));

      // 创建CSV内容
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row =>
          headers.map(header => {
            const value = (row as any)[header];
            // 处理包含逗号的值
            if (typeof value === 'string' && value.includes(',')) {
              return `"${value}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      // 创建下载链接
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `黄金价格数据_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success(`成功导出 ${filteredGoldTypes.length} 条数据`);
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    }
  };

  const handleRefresh = () => {
    loadGoldTypes();
  };

  const handleStatusToggle = async (goldTypeId: number, status: number) => {
    try {
      const goldType = goldTypes.find(type => type.id === goldTypeId);
      if (goldType) {
        const updateData = {
          ...goldType,
          status
        };
        const response = await goldTypeAPI.updateGoldType(updateData);
        if (response.success) {
          message.success('状态更新成功');
          loadGoldTypes(); // 重新加载数据
        } else {
          message.error(response.message || '状态更新失败');
        }
      }
    } catch (error) {
      console.error('状态更新失败:', error);
      message.error('状态更新失败');
    }
  };

  const filteredGoldTypes = goldTypes.filter(type => {
    const matchesSearch = type.purityCode.toLowerCase().includes(searchText.toLowerCase()) ||
                         type.itemCategory.toLowerCase().includes(searchText.toLowerCase());
    const matchesCategory = categoryFilter === 'all' || type.itemCategory === categoryFilter;
    const matchesStatus = statusFilter === 'all' ||
                         (statusFilter === 'active' && type.status === 1) ||
                         (statusFilter === 'inactive' && type.status === 0);
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const columns = [
    {
      title: '成色编码',
      dataIndex: 'purityCode',
      key: 'purityCode',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff', fontWeight: 600 }}>
          {text}
        </span>
      ),
    },
    {
      title: '类别',
      dataIndex: 'itemCategory',
      key: 'itemCategory',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <GoldOutlined style={{ color: '#faad14' }} />
          {text}
        </div>
      ),
      filters: [
        { text: '足金', value: '足金' },
        { text: 'K金', value: 'K金' },
        { text: '金条', value: '金条' },
        { text: '其他', value: '其他' },
      ],
      onFilter: (value: any, record: GoldType) => record.itemCategory === value,
    },
    {
      title: '参考价(¥/g)',
      dataIndex: 'pricePerG',
      key: 'pricePerG',
      render: (text: number) => (
        <span style={{ color: '#52c41a', fontWeight: 600 }}>
          ¥{text?.toLocaleString()}
        </span>
      ),
      sorter: (a: GoldType, b: GoldType) => (a.pricePerG || 0) - (b.pricePerG || 0),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: number) => (
        <Tag color={status === 1 ? 'success' : 'default'} icon={status === 1 ? <CheckCircleOutlined /> : <ClockCircleOutlined />}>
          {status === 1 ? '启用' : '停用'}
        </Tag>
      ),
      filters: [
        { text: '启用', value: 1 },
        { text: '停用', value: 0 },
      ],
      onFilter: (value: any, record: GoldType) => record.status === value,
    },
    {
      title: '生效时间',
      dataIndex: 'effectiveFrom',
      key: 'effectiveFrom',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ClockCircleOutlined style={{ color: '#666' }} />
          {new Date(text).toLocaleDateString()}
        </div>
      ),
      sorter: (a: GoldType, b: GoldType) => new Date(a.effectiveFrom).getTime() - new Date(b.effectiveFrom).getTime(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: GoldType) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => handleViewGoldType(record)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditGoldType(record)}
          >
            编辑
          </Button>
          <Switch
            checked={record.status === 1}
            onChange={(checked) => handleStatusToggle(record.id, checked ? 1 : 0)}
            size="small"
          />
          <Popconfirm
            title="确定要删除这个黄金价格吗？"
            onConfirm={() => handleDeleteGoldType(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              icon={<DeleteOutlined />}
              size="small"
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="page-container">
      <div className="page-content">
        {/* 页面头部 */}
        <div className="page-header">
          <h1>黄金价格管理</h1>
          <p>管理黄金价格信息，支持价格更新和状态管理</p>
        </div>

        {/* 统计信息 */}
        <div className="stats-grid">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总类型数"
                  value={totalTypes}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<GoldOutlined />}
                  suffix="种"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="启用类型"
                  value={activeTypes}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CheckCircleOutlined />}
                  suffix="种"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="禁用类型"
                  value={inactiveTypes}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<ExclamationCircleOutlined />}
                  suffix="种"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="平均单价"
                  value={avgPrice.toFixed(2)}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<DollarOutlined />}
                  suffix="¥/g"
                />
              </Card>
            </Col>
          </Row>
        </div>

        {/* 搜索和操作区域 */}
        <div className="search-section">
          <div className="search-row">
            <div className="search-item">
              <Input
                placeholder="搜索成色编码或类别"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </div>
            <div className="search-item">
              <Select
                placeholder="选择类别"
                value={categoryFilter}
                onChange={setCategoryFilter}
                style={{ width: '100%' }}
              >
                <Option value="all">全部类别</Option>
                <Option value="足金">足金</Option>
                <Option value="K金">K金</Option>
                <Option value="金条">金条</Option>
                <Option value="其他">其他</Option>
              </Select>
            </div>
            <div className="search-item">
              <Select
                placeholder="选择状态"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: '100%' }}
              >
                <Option value="all">全部状态</Option>
                <Option value="active">启用</Option>
                <Option value="inactive">禁用</Option>
              </Select>
            </div>
          </div>
          <div className="search-actions">
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              搜索
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>
              重置
            </Button>
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="action-section">
          <div className="action-buttons">
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddGoldType}>
              新增价格
            </Button>
            <Button 
              icon={<DownloadOutlined />} 
              onClick={handleExport}
              disabled={filteredGoldTypes.length === 0}
              title={filteredGoldTypes.length === 0 ? '没有数据可以导出' : '导出当前筛选结果'}
            >
              导出数据
            </Button>
            <Button icon={<SyncOutlined />} onClick={handleRefresh}>
              刷新数据
            </Button>
          </div>
          <div className="selected-info">
            共找到 {filteredGoldTypes.length} 条记录
          </div>
        </div>

        {/* 表格区域 */}
        <div className="table-section">
          <Card>
            <Table
              columns={columns}
              dataSource={filteredGoldTypes}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              scroll={{ x: 1200 }}
            />
          </Card>
        </div>

        {/* 新增/编辑黄金价格弹窗 */}
        <Modal
          title={editingGoldType ? '编辑黄金价格' : '新增黄金价格'}
          open={isModalVisible}
          onOk={handleModalOk}
          onCancel={handleModalCancel}
          width={800}
          okText="确定"
          cancelText="取消"
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              purityCode: '',
              itemCategory: '足金',
              pricePerG: 0,
              status: 1,
              effectiveFrom: new Date().toISOString().split('T')[0],
              remark: '',
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="purityCode"
                  label="成色编码"
                  rules={[{ required: true, message: '请输入成色编码' }]}
                >
                  <Input prefix={<GoldOutlined />} placeholder="请输入成色编码" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="itemCategory"
                  label="类别"
                  rules={[{ required: true, message: '请选择类别' }]}
                >
                  <Select placeholder="请选择类别">
                    <Option value="足金">足金</Option>
                    <Option value="K金">K金</Option>
                    <Option value="金条">金条</Option>
                    <Option value="其他">其他</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="pricePerG"
                  label="参考价(¥/g)"
                  rules={[{ required: true, message: '请输入参考价' }]}
                >
                  <InputNumber
                    prefix={<DollarOutlined />}
                    placeholder="请输入参考价"
                    min={0}
                    step={0.01}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="status"
                  label="状态"
                  rules={[{ required: true, message: '请选择状态' }]}
                >
                  <Select placeholder="请选择状态">
                    <Option value={1}>启用</Option>
                    <Option value={0}>停用</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="effectiveFrom"
                  label="生效时间"
                  rules={[{ required: true, message: '请选择生效时间' }]}
                >
                  <Input
                    type="date"
                    style={{ width: '100%' }}
                    placeholder="请选择生效时间"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Form.Item
              name="remark"
              label="备注"
            >
              <Input.TextArea rows={3} placeholder="请输入备注信息" />
            </Form.Item>
          </Form>
        </Modal>

        {/* 黄金价格详情抽屉 */}
        <Drawer
          title="黄金价格详情"
          placement="right"
          width={600}
          open={isDetailVisible}
          onClose={() => setIsDetailVisible(false)}
        >
          {selectedGoldType && (
            <div>
              <Descriptions title="基本信息" bordered column={1}>
                <Descriptions.Item label="成色编码">
                  <Text code>{selectedGoldType.purityCode}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="类别">
                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                    <GoldOutlined style={{ color: '#faad14' }} />
                    {selectedGoldType.itemCategory}
                  </div>
                </Descriptions.Item>
              </Descriptions>

              <Divider />

              <Descriptions title="价格信息" bordered column={1}>
                <Descriptions.Item label="参考价">
                  <span style={{ color: '#52c41a', fontWeight: 600 }}>
                    ¥{selectedGoldType.pricePerG?.toLocaleString()}/g
                  </span>
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={selectedGoldType.status === 1 ? 'success' : 'default'}>
                    {selectedGoldType.status === 1 ? '启用' : '停用'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="生效时间">
                  {new Date(selectedGoldType.effectiveFrom).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>

              <Divider />

              <Descriptions title="其他信息" bordered column={1}>
                {selectedGoldType.remark && (
                  <Descriptions.Item label="备注">
                    {selectedGoldType.remark}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </div>
          )}
        </Drawer>
      </div>
    </div>
  );
};

export default GoldTypes; 