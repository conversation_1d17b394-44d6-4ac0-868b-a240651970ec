import React from 'react';
import { Spin, Typography } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
import './Loading.css';

const { Text } = Typography;

interface LoadingProps {
  text?: string;
  size?: 'small' | 'default' | 'large';
  fullScreen?: boolean;
}

const Loading: React.FC<LoadingProps> = ({ 
  text = '加载中...', 
  size = 'large',
  fullScreen = false 
}) => {
  const antIcon = <LoadingOutlined spin />;

  if (fullScreen) {
    return (
      <div className="loading-fullscreen">
        <div className="loading-content">
          <Spin indicator={antIcon} size={size} />
          <Text className="loading-text">{text}</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="loading-container">
      <Spin indicator={antIcon} size={size} />
      <Text className="loading-text">{text}</Text>
    </div>
  );
};

export default Loading; 