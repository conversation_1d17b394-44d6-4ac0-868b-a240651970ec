import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Progress, Table, Tag, Button, Space, Alert, Spin, Typography, Divider } from 'antd';
import {
  ShoppingCartOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  DollarOutlined,
  UserOutlined,
  DatabaseOutlined,
  ApiOutlined,
  ArrowRightOutlined,
  GoldOutlined,
  ArrowUpOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  FileTextOutlined,
  PictureOutlined,
  VideoCameraOutlined,
  SettingOutlined,
  BarChartOutlined,
  CalendarOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAppStore } from '../../store';
import '../../styles/PageContainer.css';

const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { currentUser, orders, goldTypes } = useAppStore();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);

  // 实时统计数据
  const [stats, setStats] = useState({
    totalOrders: 0,
    completedOrders: 0,
    totalRevenue: 0,
    activeUsers: 0,
    totalGoldTypes: 0,
    totalQuotes: 0,
    totalInspections: 0,
    systemHealth: 98,
    memoryUsage: 68,
    networkUsage: 78,
  });

  // 最近订单数据
  const recentOrders = (orders || []).slice(0, 5).map((order, index) => ({
    key: order.id,
    customerName: order.userName || '未知客户',
    goldType: order.goldType,
    amount: order.estimatedPrice,
    status: order.status,
    date: new Date(order.createdAt).toLocaleDateString(),
    orderNumber: order.orderNumber,
  }));

  // 系统状态
  const systemStatus = [
    { name: '数据库连接', status: '正常', color: 'success', icon: <DatabaseOutlined />, value: 100 },
    { name: 'API服务', status: '正常', color: 'success', icon: <ApiOutlined />, value: 100 },
    { name: '文件存储', status: '警告', color: 'warning', icon: <ExclamationCircleOutlined />, value: 75 },
    { name: '邮件服务', status: '正常', color: 'success', icon: <CheckCircleOutlined />, value: 100 },
    { name: '短信服务', status: '错误', color: 'error', icon: <ExclamationCircleOutlined />, value: 45 },
  ];

  // 实时活动
  const recentActivities = [
    { time: '09:30', activity: '新订单 #ORD001 已创建', icon: <ShoppingCartOutlined />, type: 'order' },
    { time: '10:15', activity: '张三完成登录', icon: <UserOutlined />, type: 'user' },
    { time: '11:00', activity: '黄金价格更新: ¥456.78/g', icon: <GoldOutlined />, type: 'price' },
    { time: '12:30', activity: '订单#ORD002状态更新为已完成', icon: <ShoppingCartOutlined />, type: 'order' },
    { time: '14:00', activity: '系统备份完成', icon: <DatabaseOutlined />, type: 'system' },
    { time: '15:30', activity: '新用户注册: 李四', icon: <UserOutlined />, type: 'user' },
  ];

  // 快速操作
  const quickActions = [
    { title: '新增订单', icon: <PlusOutlined />, color: '#1890ff', path: '/orders', action: () => navigate('/orders') },
    { title: '黄金类型', icon: <GoldOutlined />, color: '#faad14', path: '/gold-types', action: () => navigate('/gold-types') },
    { title: '在线报价', icon: <FileTextOutlined />, color: '#52c41a', path: '/quotes', action: () => navigate('/quotes') },
    { title: '视频检测', icon: <VideoCameraOutlined />, color: '#722ed1', path: '/video-inspection', action: () => navigate('/video-inspection') },
  ];

  const columns = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff', fontWeight: 600 }}>
          {text}
        </span>
      ),
    },
    {
      title: '客户姓名',
      dataIndex: 'customerName',
      key: 'customerName',
    },
    {
      title: '黄金类型',
      dataIndex: 'goldType',
      key: 'goldType',
      render: (text: string) => (
        <Tag color="gold" icon={<GoldOutlined />}>
          {text}
        </Tag>
      ),
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (text: string) => (
        <span style={{ color: '#52c41a', fontWeight: 600 }}>{text}</span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = 'default';
        if (status === 'completed' || status === '已完成') color = 'success';
        else if (status === 'processing' || status === '处理中') color = 'processing';
        else if (status === 'pending' || status === '待处理') color = 'warning';
        return <Tag color={color}>{status}</Tag>;
      },
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    },
  ];

  // 更新统计数据
  useEffect(() => {
    if (orders && goldTypes) {
      setStats({
        totalOrders: orders.length || 0,
        completedOrders: orders.filter(order => order.status === 'completed').length || 0,
        totalRevenue: orders.reduce((sum, order) => {
          return sum + (order.estimatedPrice || 0);
        }, 0),
        activeUsers: 25, // 模拟数据
        totalGoldTypes: goldTypes.filter(type => type.isActive).length || 0,
        totalQuotes: 15, // 模拟数据
        totalInspections: 8, // 模拟数据
        systemHealth: 98,
        memoryUsage: 68,
        networkUsage: 78,
      });
    }
  }, [orders, goldTypes]);

  const handleRefresh = () => {
    setLoading(true);
    // 模拟刷新数据
    setTimeout(() => {
      setStats(prev => ({
        ...prev,
        systemHealth: 95 + Math.floor(Math.random() * 5),
        memoryUsage: 65 + Math.floor(Math.random() * 10),
        networkUsage: 75 + Math.floor(Math.random() * 10),
      }));
      setLoading(false);
      setRefreshKey(prev => prev + 1);
    }, 1000);
  };

  const handleViewAllOrders = () => {
    navigate('/orders');
  };

  const getActivityIconColor = (type: string) => {
    switch (type) {
      case 'order': return '#1890ff';
      case 'user': return '#52c41a';
      case 'price': return '#faad14';
      case 'system': return '#722ed1';
      default: return '#666';
    }
  };

  return (
    <div className="page-container">
      <div className="page-content">
        {/* 页面头部 */}
        <div className="page-header">
          <h1>仪表盘</h1>
          <p>欢迎回来，{currentUser?.username || '管理员'}！这里是系统的核心概览，实时监控业务数据和系统状态</p>
        </div>

        {/* 系统状态提醒 */}
        <Alert
          message="系统运行正常"
          description="所有核心服务运行正常，系统性能良好。最后检查时间：{new Date().toLocaleString()}"
          type="success"
          showIcon
          style={{ marginBottom: '24px' }}
          action={
            <Button size="small" type="link" onClick={handleRefresh} loading={loading}>
              刷新状态
            </Button>
          }
        />

        {/* 关键指标 */}
        <div className="stats-grid" style={{ padding: '0 16px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总订单数"
                  value={stats.totalOrders}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<ShoppingCartOutlined />}
                  suffix="单"
                />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  <ArrowUpOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                  较昨日 +12%
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="订单完成率"
                  value={stats.totalOrders > 0 ? Math.round((stats.completedOrders / stats.totalOrders) * 100) : 0}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CheckCircleOutlined />}
                  suffix="%"
                />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  已完成 {stats.completedOrders} 单
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总营收"
                  value={stats.totalRevenue}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<DollarOutlined />}
                  suffix="元"
                  formatter={(value) => `${((value as number) / 10000).toFixed(1)}万`}
                />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  <ArrowUpOutlined style={{ color: '#52c41a', marginRight: '4px' }} />
                  本月 +8.5%
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="活跃用户数"
                  value={stats.activeUsers}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<UserOutlined />}
                  suffix="人"
                />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  在线 {Math.floor(stats.activeUsers * 0.3)} 人
                </div>
              </Card>
            </Col>
          </Row>
        </div>

        {/* 业务统计 */}
        <div className="stats-grid" style={{ marginTop: '24px', padding: '0 16px' }}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="黄金类型"
                  value={stats.totalGoldTypes}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<GoldOutlined />}
                  suffix="种"
                />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  活跃类型 {stats.totalGoldTypes} 种
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="在线报价"
                  value={stats.totalQuotes}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<FileTextOutlined />}
                  suffix="个"
                />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  今日新增 {Math.floor(stats.totalQuotes * 0.1)} 个
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="视频检测"
                  value={stats.totalInspections}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<VideoCameraOutlined />}
                  suffix="次"
                />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  完成率 {stats.totalInspections > 0 ? Math.round((stats.totalInspections * 0.8)) : 0}%
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="系统健康度"
                  value={stats.systemHealth}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<BarChartOutlined />}
                  suffix="%"
                />
                <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                  运行时间 24天
                </div>
              </Card>
            </Col>
          </Row>
        </div>

        {/* 快速操作 */}
        <div className="card-container" style={{ marginTop: '24px', padding: '0 16px' }}>
          <Card title="快速操作" extra={<Text type="secondary">常用功能快速访问</Text>}>
            <Row gutter={[16, 16]}>
              {quickActions.map((action, index) => (
                <Col xs={12} sm={6} key={index}>
                  <Card
                    hoverable
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                    onClick={action.action}
                  >
                    <div style={{ fontSize: '32px', color: action.color, marginBottom: '8px' }}>
                      {action.icon}
                    </div>
                    <Text strong>{action.title}</Text>
                  </Card>
                </Col>
              ))}
            </Row>
          </Card>
        </div>

        <Row gutter={24} style={{ marginTop: '24px', padding: '0 16px' }}>
          {/* 左侧内容 */}
          <Col xs={24} lg={16}>
            {/* 最近订单 */}
            <Card 
              title="最近订单" 
              className="card-container"
              extra={
                <Button type="link" icon={<ArrowRightOutlined />} onClick={handleViewAllOrders}>
                  查看全部
                </Button>
              }
            >
              <Table
                columns={columns}
                dataSource={recentOrders}
                pagination={false}
                size="small"
                loading={loading}
                rowKey="key"
                locale={{ emptyText: '暂无订单数据' }}
              />
            </Card>

            {/* 系统状态监控 */}
            <Card 
              title="系统状态监控" 
              className="card-container" 
              style={{ marginTop: '16px' }}
              extra={<Button icon={<ReloadOutlined />} onClick={handleRefresh} loading={loading}>刷新</Button>}
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} lg={8}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ marginBottom: '16px' }}>
                      <Text strong>系统健康度</Text>
                    </div>
                    <Progress 
                      type="circle" 
                      percent={stats.systemHealth} 
                      strokeColor={stats.systemHealth > 90 ? '#52c41a' : stats.systemHealth > 70 ? '#faad14' : '#ff4d4f'}
                      format={(percent) => `${percent}%`}
                    />
                  </div>
                </Col>
                <Col xs={24} sm={12} lg={8}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ marginBottom: '16px' }}>
                      <Text strong>内存使用率</Text>
                    </div>
                    <Progress 
                      type="circle" 
                      percent={stats.memoryUsage} 
                      strokeColor={stats.memoryUsage > 80 ? '#ff4d4f' : stats.memoryUsage > 60 ? '#faad14' : '#52c41a'}
                      format={(percent) => `${percent}%`}
                    />
                  </div>
                </Col>
                <Col xs={24} sm={12} lg={8}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ marginBottom: '16px' }}>
                      <Text strong>网络带宽</Text>
                    </div>
                    <Progress 
                      type="circle" 
                      percent={stats.networkUsage} 
                      strokeColor={stats.networkUsage > 80 ? '#ff4d4f' : stats.networkUsage > 60 ? '#faad14' : '#52c41a'}
                      format={(percent) => `${percent}%`}
                    />
                  </div>
                </Col>
              </Row>
            </Card>
          </Col>

          {/* 右侧边栏 */}
          <Col xs={24} lg={8}>
            {/* 服务状态 */}
            <Card title="服务状态" className="card-container" style={{ marginBottom: '16px' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                {systemStatus.map((item, index) => (
                  <div key={index} style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    padding: '8px 0',
                    borderBottom: index < systemStatus.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <span style={{ color: getActivityIconColor('system') }}>{item.icon}</span>
                      <span>{item.name}</span>
                    </div>
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Progress 
                        percent={item.value} 
                        size="small" 
                        showInfo={false}
                        strokeColor={item.color === 'success' ? '#52c41a' : item.color === 'warning' ? '#faad14' : '#ff4d4f'}
                        style={{ width: '60px' }}
                      />
                      <Tag color={item.color}>{item.status}</Tag>
                    </div>
                  </div>
                ))}
              </Space>
            </Card>

            {/* 实时活动 */}
            <Card title="实时活动" className="card-container">
              <Space direction="vertical" style={{ width: '100%' }}>
                {recentActivities.map((item, index) => (
                  <div key={index} style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    gap: '8px',
                    padding: '8px 0',
                    borderBottom: index < recentActivities.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}>
                    <span style={{ 
                      color: getActivityIconColor(item.type), 
                      fontWeight: 500,
                      minWidth: '40px'
                    }}>
                      {item.time}
                    </span>
                    <span style={{ color: '#666', flex: 1 }}>{item.activity}</span>
                    <span style={{ color: getActivityIconColor(item.type) }}>{item.icon}</span>
                  </div>
                ))}
              </Space>
            </Card>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default Dashboard; 