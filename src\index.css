:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

body {
  margin: 0;
  padding: 0;
  background: #f0f2f5;
  overflow-x: hidden;
  box-sizing: border-box;
  min-height: 100vh;
  width: 100%;
}

#root {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  box-sizing: border-box;
}

/* 全局重置 */
* {
  box-sizing: border-box;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

/* 响应式工具类 */
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }
  
  body {
    padding: 0;
  }
  
  #root {
    padding: 0;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
}

/* 修复移动端触摸问题 */
@media (hover: none) and (pointer: coarse) {
  .ant-btn:hover,
  .ant-card:hover {
    transform: none !important;
  }
}

/* 确保所有容器都有正确的盒模型 */
.ant-layout-content,
.ant-layout-sider,
.ant-layout-header {
  box-sizing: border-box;
}

/* 修复布局问题 */
.ant-layout {
  min-height: 100vh;
  width: 100%;
}

.ant-layout-content {
  width: 100%;
  overflow-x: hidden;
}

/* 深色模式基础样式 */
@media (prefers-color-scheme: dark) {
  body {
    background: #141414;
    color: #d9d9d9;
  }
  
  #root {
    background: #141414;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ant-layout-sider,
  .ant-layout-header,
  .ant-layout-content {
    border: 1px solid #000;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 打印样式 */
@media print {
  .ant-layout-sider,
  .ant-layout-header {
    display: none !important;
  }
  
  .ant-layout-content {
    margin: 0 !important;
    padding: 0 !important;
    width: 100% !important;
  }
}
