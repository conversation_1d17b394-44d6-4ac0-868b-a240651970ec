import React, { useState } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  Select, 
  DatePicker, 
  Modal, 
  Form, 
  InputNumber, 
  Popconfirm, 
  Badge, 
  Row, 
  Col,
  Drawer,
  Descriptions,
  Divider,
  Typography,
  Statistic,
  Alert,
  message
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined,
  FileTextOutlined,
  UserOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  GoldOutlined,
  DollarOutlined,
  CalendarOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useAppStore } from '../../store';
import '../../styles/PageContainer.css';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;

const Orders: React.FC = () => {
  const { orders, setOrders } = useAppStore();
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<any>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [editingOrder, setEditingOrder] = useState<any>(null);
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [form] = Form.useForm();

  // 计算统计数据
  const totalOrders = orders.length;
  const completedOrders = orders.filter(order => order.status === 'completed').length;
  const pendingOrders = orders.filter(order => order.status === 'pending').length;
  const processingOrders = orders.filter(order => order.status === 'processing').length;
  const totalValue = orders.reduce((sum, order) => sum + (order.estimatedPrice || 0), 0);

  const handleAddOrder = () => {
    setEditingOrder(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditOrder = (record: any) => {
    setEditingOrder(record);
    form.setFieldsValue({
      userName: record.userName,
      userPhone: record.userPhone,
      userAddress: record.userAddress,
      goldType: record.goldType,
      weight: record.weight,
      estimatedPrice: record.estimatedPrice,
      remarks: record.remarks,
    });
    setIsModalVisible(true);
  };

  const handleViewOrder = (record: any) => {
    setSelectedOrder(record);
    setIsDetailVisible(true);
  };

  const handleDeleteOrder = (orderId: string) => {
    const updatedOrders = orders.filter(order => order.id !== orderId);
    setOrders(updatedOrders);
  };

  const handleModalOk = () => {
    form.validateFields().then(values => {
      if (editingOrder) {
        // 编辑订单
        const updatedOrders = orders.map(order => 
          order.id === editingOrder.id 
            ? { ...order, ...values, updatedAt: new Date().toISOString() }
            : order
        );
        setOrders(updatedOrders);
      } else {
        // 新增订单
        const newOrder = {
          id: `ORD${Date.now()}`,
          orderNumber: `ORD${Date.now()}`,
          ...values,
          status: '待处理',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };
        setOrders([...orders, newOrder]);
      }
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const handleSearch = () => {
    // 搜索逻辑
  };

  const handleReset = () => {
    setSearchText('');
    setStatusFilter('all');
    setDateRange(null);
  };

  const handleExport = () => {
    if (filteredOrders.length === 0) {
      message.warning('没有数据可以导出');
      return;
    }

    try {
      // 准备导出数据
      const exportData = filteredOrders.map(order => ({
        '订单号': order.orderNumber,
        '客户姓名': order.userName,
        '联系电话': order.userPhone,
        '客户地址': order.userAddress,
        '黄金类型': order.goldType,
        '重量(g)': order.weight,
        '预估价格': order.estimatedPrice,
        '状态': order.status,
        '备注': order.remarks || '',
        '创建时间': order.createdAt,
        '更新时间': order.updatedAt,
      }));

      // 创建CSV内容
      const headers = Object.keys(exportData[0]);
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => 
          headers.map(header => {
            const value = row[header];
            // 处理包含逗号的值
            if (typeof value === 'string' && value.includes(',')) {
              return `"${value}"`;
            }
            return value;
          }).join(',')
        )
      ].join('\n');

      // 创建下载链接
      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `订单数据_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success(`成功导出 ${filteredOrders.length} 条数据`);
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    }
  };

  const handleRefresh = () => {
    // 刷新逻辑
  };

  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.userName.toLowerCase().includes(searchText.toLowerCase()) ||
                         order.orderNumber.toLowerCase().includes(searchText.toLowerCase());
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter;
    const matchesDate = !dateRange || (
      new Date(order.createdAt) >= dateRange[0].toDate() &&
      new Date(order.createdAt) <= dateRange[1].toDate()
    );
    return matchesSearch && matchesStatus && matchesDate;
  });

  const columns = [
    {
      title: '订单号',
      dataIndex: 'orderNumber',
      key: 'orderNumber',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff', fontWeight: 600 }}>
          {text}
        </span>
      ),
    },
    {
      title: '客户姓名',
      dataIndex: 'userName',
      key: 'userName',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <UserOutlined style={{ color: '#1890ff' }} />
          {text}
        </div>
      ),
    },
    {
      title: '黄金类型',
      dataIndex: 'goldType',
      key: 'goldType',
      render: (text: string) => (
        <Tag color="gold" icon={<GoldOutlined />}>
          {text}
        </Tag>
      ),
    },
    {
      title: '重量(g)',
      dataIndex: 'weight',
      key: 'weight',
      render: (text: string) => (
        <span style={{ fontWeight: 600 }}>{text}</span>
      ),
    },
    {
      title: '预估价格',
      dataIndex: 'estimatedPrice',
      key: 'estimatedPrice',
      render: (text: string) => (
        <span style={{ color: '#52c41a', fontWeight: 600 }}>{text}</span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => {
        let color = 'default';
        let icon = null;
        if (status === '已完成') {
          color = 'success';
          icon = <CheckCircleOutlined />;
        } else if (status === '处理中') {
          color = 'processing';
          icon = <ClockCircleOutlined />;
        } else if (status === '待处理') {
          color = 'warning';
          icon = <ExclamationCircleOutlined />;
        }
        return (
          <Tag color={color} icon={icon}>
            {status}
          </Tag>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <CalendarOutlined style={{ color: '#666' }} />
          {new Date(text).toLocaleDateString()}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleViewOrder(record)}
          >
            查看
          </Button>
          <Button 
            type="text" 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => handleEditOrder(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个订单吗？"
            onConfirm={() => handleDeleteOrder(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="page-container">
      <div className="page-content">
        {/* 页面头部 */}
        <div className="page-header">
          <h1>订单管理</h1>
          <p>管理所有黄金回收订单，包括订单创建、编辑、状态跟踪和完成确认</p>
        </div>

        {/* 统计信息 */}
        <div className="stats-grid">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总订单数"
                  value={totalOrders}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<FileTextOutlined />}
                  suffix="单"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="已完成"
                  value={completedOrders}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CheckCircleOutlined />}
                  suffix="单"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="处理中"
                  value={processingOrders}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<ClockCircleOutlined />}
                  suffix="单"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总价值"
                  value={totalValue}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<DollarOutlined />}
                  suffix="元"
                  formatter={(value) => `${(value / 10000).toFixed(1)}万`}
                />
              </Card>
            </Col>
          </Row>
        </div>

        {/* 搜索和操作区域 */}
        <div className="search-section">
          <div className="search-row">
            <div className="search-item">
              <Input
                placeholder="搜索客户姓名或订单号"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                allowClear
              />
            </div>
            <div className="search-item">
              <Select
                placeholder="选择状态"
                value={statusFilter}
                onChange={setStatusFilter}
                style={{ width: '100%' }}
              >
                <Option value="all">全部状态</Option>
                <Option value="待处理">待处理</Option>
                <Option value="处理中">处理中</Option>
                <Option value="已完成">已完成</Option>
              </Select>
            </div>
            <div className="search-item">
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
              />
            </div>
          </div>
          <div className="search-actions">
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              搜索
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>
              重置
            </Button>
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="action-section">
          <div className="action-buttons">
            <Button type="primary" icon={<PlusOutlined />} onClick={handleAddOrder}>
              新增订单
            </Button>
            <Button 
              icon={<DownloadOutlined />} 
              onClick={handleExport}
              disabled={filteredOrders.length === 0}
              title={filteredOrders.length === 0 ? '没有数据可以导出' : '导出当前筛选结果'}
            >
              导出数据
            </Button>
            <Button icon={<SyncOutlined />} onClick={handleRefresh}>
              刷新数据
            </Button>
          </div>
          <div className="selected-info">
            共找到 {filteredOrders.length} 条记录
          </div>
        </div>

        {/* 表格区域 */}
        <div className="table-section">
          <Card>
            <Table
              columns={columns}
              dataSource={filteredOrders}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
              scroll={{ x: 1200 }}
            />
          </Card>
        </div>

        {/* 新增/编辑订单弹窗 */}
        <Modal
          title={editingOrder ? '编辑订单' : '新增订单'}
          open={isModalVisible}
          onOk={handleModalOk}
          onCancel={handleModalCancel}
          width={800}
          okText="确定"
          cancelText="取消"
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
                      userName: '',
        userPhone: '',
        userAddress: '',
        goldType: '',
        weight: '',
        estimatedPrice: '',
        remarks: '',
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="userName"
                  label="客户姓名"
                  rules={[{ required: true, message: '请输入客户姓名' }]}
                >
                  <Input prefix={<UserOutlined />} placeholder="请输入客户姓名" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="userPhone"
                  label="联系电话"
                  rules={[{ required: true, message: '请输入联系电话' }]}
                >
                  <Input prefix={<PhoneOutlined />} placeholder="请输入联系电话" />
                </Form.Item>
              </Col>
            </Row>
                            <Form.Item
                  name="userAddress"
                  label="客户地址"
                  rules={[{ required: true, message: '请输入客户地址' }]}
                >
                  <Input prefix={<EnvironmentOutlined />} placeholder="请输入客户地址" />
                </Form.Item>
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="goldType"
                  label="黄金类型"
                  rules={[{ required: true, message: '请选择黄金类型' }]}
                >
                  <Select placeholder="请选择黄金类型">
                    <Option value="足金999">足金999</Option>
                    <Option value="足金990">足金990</Option>
                    <Option value="K金18K">K金18K</Option>
                    <Option value="K金14K">K金14K</Option>
                    <Option value="金条">金条</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="weight"
                  label="重量(g)"
                  rules={[{ required: true, message: '请输入重量' }]}
                >
                  <InputNumber
                    prefix={<GoldOutlined />}
                    placeholder="请输入重量"
                    min={0}
                    step={0.01}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="goldPurity"
                  label="纯度(%)"
                  rules={[{ required: true, message: '请输入纯度' }]}
                >
                  <InputNumber
                    prefix={<GoldOutlined />}
                    placeholder="请输入纯度"
                    min={0}
                    max={100}
                    step={0.1}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="estimatedPrice"
                  label="预估价格"
                  rules={[{ required: true, message: '请输入预估价格' }]}
                >
                  <Input prefix={<DollarOutlined />} placeholder="请输入预估价格" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="remarks"
                  label="备注"
                >
                  <TextArea rows={3} placeholder="请输入备注信息" />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Modal>

        {/* 订单详情抽屉 */}
        <Drawer
          title="订单详情"
          placement="right"
          width={600}
          open={isDetailVisible}
          onClose={() => setIsDetailVisible(false)}
        >
          {selectedOrder && (
            <div>
              <Descriptions title="基本信息" bordered column={1}>
                <Descriptions.Item label="订单号">
                  <Text code>{selectedOrder.orderNumber}</Text>
                </Descriptions.Item>
                <Descriptions.Item label="客户姓名">
                  {selectedOrder.userName}
                </Descriptions.Item>
                <Descriptions.Item label="联系电话">
                  {selectedOrder.userPhone}
                </Descriptions.Item>
                <Descriptions.Item label="客户地址">
                  {selectedOrder.userAddress}
                </Descriptions.Item>
              </Descriptions>
              
              <Divider />
              
              <Descriptions title="黄金信息" bordered column={1}>
                <Descriptions.Item label="黄金类型">
                  <Tag color="gold" icon={<GoldOutlined />}>
                    {selectedOrder.goldType}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="重量">
                  {selectedOrder.weight} g
                </Descriptions.Item>
                <Descriptions.Item label="预估价格">
                  <Text strong style={{ color: '#52c41a' }}>
                    {selectedOrder.estimatedPrice}
                  </Text>
                </Descriptions.Item>
              </Descriptions>
              
              <Divider />
              
              <Descriptions title="订单信息" bordered column={1}>
                <Descriptions.Item label="订单状态">
                  <Tag color={
                    selectedOrder.status === '已完成' ? 'success' :
                    selectedOrder.status === '处理中' ? 'processing' : 'warning'
                  }>
                    {selectedOrder.status}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {new Date(selectedOrder.createdAt).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {new Date(selectedOrder.updatedAt).toLocaleString()}
                </Descriptions.Item>
                {selectedOrder.remarks && (
                  <Descriptions.Item label="备注">
                    {selectedOrder.remarks}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </div>
          )}
        </Drawer>
      </div>
    </div>
  );
};

export default Orders; 