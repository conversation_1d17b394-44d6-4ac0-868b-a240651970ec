.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.loading-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.loading-text {
  margin-top: 16px;
  color: #8c8c8c;
  font-size: 14px;
}

.loading-container .ant-spin {
  margin-bottom: 8px;
}

.loading-container .ant-spin-dot {
  font-size: 24px;
}

.loading-container .ant-spin-dot i {
  background-color: #1890ff;
}

.loading-container .ant-spin-text {
  color: #1890ff;
  font-size: 14px;
  margin-top: 8px;
}

/* 自定义加载动画 */
.loading-container .ant-spin-dot-item {
  background-color: #1890ff;
  border-radius: 50%;
}

.loading-container .ant-spin-dot-item:nth-child(1) {
  animation: loading-dot-1 1.2s infinite ease-in-out;
}

.loading-container .ant-spin-dot-item:nth-child(2) {
  animation: loading-dot-2 1.2s infinite ease-in-out;
}

.loading-container .ant-spin-dot-item:nth-child(3) {
  animation: loading-dot-3 1.2s infinite ease-in-out;
}

.loading-container .ant-spin-dot-item:nth-child(4) {
  animation: loading-dot-4 1.2s infinite ease-in-out;
}

@keyframes loading-dot-1 {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes loading-dot-2 {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes loading-dot-3 {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes loading-dot-4 {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 全屏加载时的特殊样式 */
.loading-fullscreen .loading-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 48px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.loading-fullscreen .ant-spin-dot {
  font-size: 32px;
}

.loading-fullscreen .loading-text {
  font-size: 16px;
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-container {
    padding: 24px;
  }
  
  .loading-fullscreen .loading-content {
    padding: 32px 24px;
  }
  
  .loading-fullscreen .ant-spin-dot {
    font-size: 24px;
  }
  
  .loading-fullscreen .loading-text {
    font-size: 14px;
    margin-top: 16px;
  }
}

@media (max-width: 480px) {
  .loading-container {
    padding: 16px;
  }
  
  .loading-fullscreen .loading-content {
    padding: 24px 16px;
    margin: 16px;
  }
  
  .loading-fullscreen .ant-spin-dot {
    font-size: 20px;
  }
  
  .loading-fullscreen .loading-text {
    font-size: 13px;
    margin-top: 12px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .loading-fullscreen {
    background: rgba(0, 0, 0, 0.8);
  }
  
  .loading-fullscreen .loading-content {
    background: rgba(30, 30, 30, 0.95);
    color: #fff;
  }
  
  .loading-text {
    color: #d9d9d9;
  }
  
  .loading-container .ant-spin-dot-item {
    background-color: #40a9ff;
  }
  
  .loading-container .ant-spin-text {
    color: #40a9ff;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .loading-fullscreen {
    background: rgba(0, 0, 0, 0.95);
  }
  
  .loading-fullscreen .loading-content {
    border: 2px solid #fff;
  }
  
  .loading-container .ant-spin-dot-item {
    background-color: #fff;
  }
  
  .loading-container .ant-spin-text {
    color: #fff;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .loading-container .ant-spin-dot-item {
    animation: none;
  }
  
  .loading-container .ant-spin {
    animation: none;
  }
}

/* 打印样式 */
@media print {
  .loading-fullscreen {
    display: none;
  }
  
  .loading-container {
    display: none;
  }
} 