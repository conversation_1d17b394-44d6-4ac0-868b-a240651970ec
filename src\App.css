.app {
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  position: relative;
}

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-x: hidden;
}

#root {
  height: 100%;
  width: 100%;
  overflow-x: hidden;
}

/* Ant Design 组件样式覆盖 */
.ant-btn {
  border-radius: var(--border-radius, 6px);
  box-sizing: border-box;
}

.ant-input,
.ant-select-selector,
.ant-input-number,
.ant-picker,
.ant-cascader-picker {
  border-radius: var(--border-radius, 6px);
  box-sizing: border-box;
}

.ant-card {
  border-radius: var(--border-radius, 6px);
  box-sizing: border-box;
}

.ant-modal-content,
.ant-drawer-content {
  border-radius: var(--border-radius, 6px);
  box-sizing: border-box;
}

.ant-tag {
  border-radius: calc(var(--border-radius, 6px) - 2px);
  box-sizing: border-box;
}

.ant-badge {
  border-radius: calc(var(--border-radius, 6px) - 2px);
  box-sizing: border-box;
}

/* 布局组件样式 - 修复冲突 */
.ant-layout {
  min-height: 100vh;
  width: 100%;
}

.ant-layout-content {
  width: 100%;
  box-sizing: border-box;
}

.ant-layout-sider {
  box-sizing: border-box;
}

.ant-layout-header {
  box-sizing: border-box;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 深色模式支持 */
.dark-mode {
  --primary-color: #1890ff;
  --border-radius: 6px;
  --font-size: 14px;
}

.dark-mode .ant-layout {
  background: #141414;
}

.dark-mode .ant-card {
  background: #1f1f1f;
  border-color: #434343;
}

.dark-mode .ant-card-head {
  background: #262626;
  border-bottom-color: #434343;
}

.dark-mode .ant-card-head-title {
  color: #d9d9d9;
}

.dark-mode .ant-table {
  background: #1f1f1f;
}

.dark-mode .ant-table-thead > tr > th {
  background: #262626;
  border-bottom-color: #434343;
  color: #d9d9d9;
}

.dark-mode .ant-table-tbody > tr > td {
  border-bottom-color: #434343;
  color: #d9d9d9;
}

.dark-mode .ant-table-tbody > tr:hover > td {
  background: #262626;
}

.dark-mode .ant-pagination-item {
  background: #1f1f1f;
  border-color: #434343;
}

.dark-mode .ant-pagination-item a {
  color: #d9d9d9;
}

.dark-mode .ant-pagination-item:hover {
  border-color: #1890ff;
}

.dark-mode .ant-pagination-item-active {
  border-color: #1890ff;
  background: #1890ff;
}

.dark-mode .ant-pagination-item-active a {
  color: white;
}

.dark-mode .ant-modal-content {
  background: #1f1f1f;
}

.dark-mode .ant-modal-header {
  background: #262626;
  border-bottom-color: #434343;
}

.dark-mode .ant-modal-title {
  color: #d9d9d9;
}

.dark-mode .ant-modal-close {
  color: #d9d9d9;
}

.dark-mode .ant-drawer-content {
  background: #1f1f1f;
}

.dark-mode .ant-drawer-header {
  background: #262626;
  border-bottom-color: #434343;
}

.dark-mode .ant-drawer-title {
  color: #d9d9d9;
}

.dark-mode .ant-drawer-close {
  color: #d9d9d9;
}

.dark-mode .ant-form-item-label > label {
  color: #d9d9d9;
}

.dark-mode .ant-input,
.dark-mode .ant-select-selector,
.dark-mode .ant-input-number,
.dark-mode .ant-picker {
  background: #262626;
  border-color: #434343;
  color: #d9d9d9;
}

.dark-mode .ant-input::placeholder,
.dark-mode .ant-select-selection-placeholder {
  color: #737373;
}

.dark-mode .ant-select-arrow {
  color: #d9d9d9;
}

.dark-mode .ant-switch {
  background: #434343;
}

.dark-mode .ant-switch-checked {
  background: #1890ff;
}

.dark-mode .ant-color-picker-trigger {
  background: #262626;
  border-color: #434343;
}

.dark-mode .ant-tabs-tab {
  color: #d9d9d9;
}

.dark-mode .ant-tabs-tab-active {
  color: #1890ff;
}

.dark-mode .ant-tabs-ink-bar {
  background: #1890ff;
}

.dark-mode .ant-menu {
  background: #001529;
}

.dark-mode .ant-menu-item,
.dark-mode .ant-menu-submenu-title {
  color: #a6adb4;
}

.dark-mode .ant-menu-item:hover,
.dark-mode .ant-menu-submenu-title:hover {
  color: #1890ff;
  background: #1890ff1a;
}

.dark-mode .ant-menu-item-selected {
  background: #1890ff;
  color: white;
}

.dark-mode .ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: #1890ff;
}

.dark-mode .ant-menu-submenu-open > .ant-menu-submenu-title {
  color: #1890ff;
}

/* 紧凑模式支持 */
.compact-mode {
  --border-radius: 4px;
}

.compact-mode .ant-btn {
  padding: 4px 12px;
  height: 32px;
  font-size: 13px;
}

.compact-mode .ant-input,
.compact-mode .ant-select-selector,
.compact-mode .ant-input-number,
.compact-mode .ant-picker {
  padding: 4px 8px;
  height: 32px;
}

.compact-mode .ant-card {
  padding: 16px;
}

.compact-mode .ant-card-head {
  padding: 0 16px;
}

.compact-mode .ant-card-head-title {
  font-size: 15px;
}

.compact-mode .ant-table-thead > tr > th {
  padding: 8px 12px;
}

.compact-mode .ant-table-tbody > tr > td {
  padding: 8px 12px;
}

.compact-mode .ant-form-item {
  margin-bottom: 16px;
}

.compact-mode .ant-form-item-label {
  padding-bottom: 4px;
}

.compact-mode .ant-tabs-tab {
  padding: 8px 16px;
}

.compact-mode .ant-tabs-content-holder {
  padding: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app {
    width: 100%;
  }
  
  .ant-card {
    margin-bottom: 16px;
  }
  
  .ant-table {
    font-size: 13px;
  }
  
  .ant-btn {
    font-size: 13px;
    padding: 4px 12px;
  }
  
  .ant-layout-content {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .ant-card {
    margin-bottom: 12px;
  }
  
  .ant-table {
    font-size: 12px;
  }
  
  .ant-btn {
    font-size: 12px;
    padding: 3px 10px;
  }
  
  .ant-form-item {
    margin-bottom: 12px;
  }
  
  .ant-layout-content {
    padding: 12px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .ant-btn {
    border-width: 2px;
  }
  
  .ant-input,
  .ant-select-selector,
  .ant-input-number,
  .ant-picker {
    border-width: 2px;
  }
  
  .ant-card {
    border-width: 2px;
  }
  
  .ant-table-thead > tr > th {
    border-bottom-width: 2px;
  }
  
  .ant-table-tbody > tr > td {
    border-bottom-width: 1px;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .ant-btn,
  .ant-input,
  .ant-select-selector,
  .ant-input-number,
  .ant-picker,
  .ant-card,
  .ant-table,
  .ant-tabs-tab,
  .ant-menu-item {
    transition: none;
  }
  
  .ant-btn:hover,
  .ant-input:hover,
  .ant-select-selector:hover,
  .ant-input-number:hover,
  .ant-picker:hover {
    transform: none;
  }
}

/* 打印样式 */
@media print {
  .app {
    background: white;
  }
  
  .ant-card {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .ant-table {
    border: 1px solid #000;
  }
  
  .ant-table-thead > tr > th,
  .ant-table-tbody > tr > td {
    border: 1px solid #000;
  }
  
  .ant-btn {
    border: 1px solid #000;
    background: white;
    color: black;
  }
}

