import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ConfigProvider, theme } from 'antd';

export interface ThemeConfig {
  primaryColor: string;
  borderRadius: number;
  compactMode: boolean;
  darkMode: boolean;
  fontSize: string;
}

interface ThemeContextType {
  themeConfig: ThemeConfig;
  updateTheme: (config: Partial<ThemeConfig>) => void;
  resetTheme: () => void;
}

const defaultThemeConfig: ThemeConfig = {
  primaryColor: '#1890ff',
  borderRadius: 6,
  compactMode: false,
  darkMode: false,
  fontSize: 'medium',
};

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [themeConfig, setThemeConfig] = useState<ThemeConfig>(() => {
    // 从localStorage加载保存的主题配置
    const savedTheme = localStorage.getItem('themeConfig');
    if (savedTheme) {
      try {
        return { ...defaultThemeConfig, ...JSON.parse(savedTheme) };
      } catch (error) {
        console.error('Failed to parse saved theme config:', error);
      }
    }
    return defaultThemeConfig;
  });

  // 应用主题配置到CSS变量和全局样式
  useEffect(() => {
    const root = document.documentElement;
    
    // 设置CSS变量
    root.style.setProperty('--primary-color', themeConfig.primaryColor);
    root.style.setProperty('--border-radius', `${themeConfig.borderRadius}px`);
    root.style.setProperty('--font-size', themeConfig.fontSize);
    
    // 应用深色模式
    if (themeConfig.darkMode) {
      root.classList.add('dark-mode');
      document.body.classList.add('dark-mode');
    } else {
      root.classList.remove('dark-mode');
      document.body.classList.remove('dark-mode');
    }
    
    // 应用紧凑模式
    if (themeConfig.compactMode) {
      root.classList.add('compact-mode');
      document.body.classList.add('compact-mode');
    } else {
      root.classList.remove('compact-mode');
      document.body.classList.remove('compact-mode');
    }
    
    // 保存到localStorage
    localStorage.setItem('themeConfig', JSON.stringify(themeConfig));
  }, [themeConfig]);

  const updateTheme = (config: Partial<ThemeConfig>) => {
    setThemeConfig(prev => ({ ...prev, ...config }));
  };

  const resetTheme = () => {
    setThemeConfig(defaultThemeConfig);
  };

  // 生成Ant Design主题配置
  const antdTheme = {
    token: {
      colorPrimary: themeConfig.primaryColor,
      borderRadius: themeConfig.borderRadius,
      fontSize: themeConfig.fontSize === 'small' ? 12 : themeConfig.fontSize === 'large' ? 16 : 14,
      // 深色模式下的颜色配置
      colorBgContainer: themeConfig.darkMode ? '#1f1f1f' : '#ffffff',
      colorBgLayout: themeConfig.darkMode ? '#141414' : '#f0f2f5',
      colorBgElevated: themeConfig.darkMode ? '#262626' : '#ffffff',
      colorBorder: themeConfig.darkMode ? '#434343' : '#d9d9d9',
      colorText: themeConfig.darkMode ? '#d9d9d9' : '#262626',
      colorTextSecondary: themeConfig.darkMode ? '#a6a6a6' : '#595959',
      colorTextTertiary: themeConfig.darkMode ? '#737373' : '#8c8c8c',
      colorFill: themeConfig.darkMode ? '#434343' : '#f5f5f5',
      colorFillSecondary: themeConfig.darkMode ? '#303030' : '#fafafa',
      colorFillTertiary: themeConfig.darkMode ? '#1f1f1f' : '#f5f5f5',
      colorFillQuaternary: themeConfig.darkMode ? '#141414' : '#fafafa',
    },
    algorithm: themeConfig.darkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
    compact: themeConfig.compactMode ? 'compact' : 'default',
  };

  return (
    <ThemeContext.Provider value={{ themeConfig, updateTheme, resetTheme }}>
      <ConfigProvider theme={antdTheme}>
        {children}
      </ConfigProvider>
    </ThemeContext.Provider>
  );
}; 