# 黄金回收后台管理系统

## 项目概述
这是黄金回收小程序的配套后台管理系统，提供完整的订单管理、用户管理、价格管理、视频质检等功能。基于React + TypeScript + Ant Design构建，具有现代化的UI设计和良好的用户体验。

## 技术栈
- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Ant Design 5
- **状态管理**: Zustand
- **路由管理**: React Router 6
- **HTTP客户端**: Axios
- **日期处理**: Day.js
- **样式**: CSS3 + Ant Design 主题

## 主要功能

### 1. 仪表板 (Dashboard)
- 系统概览统计
- 订单数量、收入、用户等关键指标
- 最近订单列表
- 系统状态监控
- 快速操作入口

### 2. 订单管理 (Orders)
- 订单列表展示
- 多条件搜索和筛选
- 订单状态管理
- 订单详情查看
- 订单编辑和删除
- 批量操作支持

### 3. 黄金类型管理 (Gold Types)
- 黄金类型配置
- 价格设置和更新
- 类型启用/禁用
- 纯度信息管理

### 4. 估价记录 (Quotes)
- 估价历史查询
- 估价数据统计
- 图片查看
- 价格趋势分析

### 5. 视频质检 (Video Inspections)
- 质检视频管理
- 检测进度跟踪
- 质检报告生成
- 质检员分配

### 6. 用户管理 (Users)
- 用户信息管理
- 用户权限控制
- 用户行为分析
- 账户状态管理

### 7. 系统设置 (Settings)
- 系统参数配置
- 权限管理
- 日志查看
- 数据备份

## 项目结构
```
src/
├── components/          # 公共组件
│   └── Layout/         # 布局组件
│       ├── MainLayout.tsx
│       └── MainLayout.css
├── pages/              # 页面组件
│   ├── Login/          # 登录页面
│   ├── Dashboard/      # 仪表板
│   ├── Orders/         # 订单管理
│   ├── GoldTypes/      # 黄金类型管理
│   ├── Quotes/         # 估价记录
│   ├── VideoInspections/ # 视频质检
│   ├── Users/          # 用户管理
│   └── Settings/       # 系统设置
├── services/           # API服务
│   └── api.ts         # API接口定义
├── store/              # 状态管理
│   └── index.ts       # Zustand store
├── types/              # 类型定义
│   └── index.ts       # TypeScript接口
├── utils/              # 工具函数
├── App.tsx            # 应用入口
├── main.tsx           # 主入口
└── index.css          # 全局样式
```

## 安装和运行

### 环境要求
- Node.js 16+
- npm 或 yarn

### 安装依赖
```bash
npm install
# 或
yarn install
```

### 开发环境运行
```bash
npm run dev
# 或
yarn dev
```

### 生产环境构建
```bash
npm run build
# 或
yarn build
```

### 代码检查
```bash
npm run lint
# 或
yarn lint
```

## 快速开始

### 1. 启动项目
```bash
cd gold-recovery-pipe
npm install
npm run dev
```

### 2. 访问系统
打开浏览器访问 `http://localhost:5173`

### 3. 登录系统
- 用户名：`admin`
- 密码：`admin123`

## 环境配置

### 环境变量
创建 `.env.local` 文件：
```env
VITE_API_BASE_URL=http://localhost:3000/api
VITE_APP_TITLE=黄金回收管理系统
```

### API配置
在 `src/services/api.ts` 中配置API基础URL和认证信息。

## 开发指南

### 添加新页面
1. 在 `src/pages/` 下创建新页面组件
2. 在 `src/App.tsx` 中添加路由
3. 在 `src/components/Layout/MainLayout.tsx` 中添加菜单项

### 添加新API
1. 在 `src/services/api.ts` 中定义新的API函数
2. 在 `src/types/index.ts` 中添加相关类型定义
3. 在页面组件中调用API

### 状态管理
使用 Zustand 进行状态管理，在 `src/store/index.ts` 中定义全局状态。

## 功能特性

### 响应式设计
- 支持桌面端和移动端
- 自适应布局
- 触摸友好的交互

### 主题定制
- 支持明暗主题切换
- 可自定义颜色方案
- 组件样式统一

### 性能优化
- 代码分割
- 懒加载
- 缓存策略
- 图片优化

### 安全性
- 路由保护
- 权限控制
- 数据验证
- XSS防护

## 页面说明

### 登录页面
- 美观的渐变背景设计
- 表单验证和错误提示
- 响应式布局适配

### 主布局
- 可折叠侧边栏
- 顶部导航栏
- 用户信息和通知
- 面包屑导航

### 仪表板
- 数据统计卡片
- 图表展示
- 快速操作入口
- 系统状态监控

### 订单管理
- 高级搜索和筛选
- 表格排序和分页
- 批量操作支持
- 状态管理

### 黄金类型管理
- CRUD操作
- 价格设置
- 状态切换
- 搜索功能

## 部署说明

### 构建生产版本
```bash
npm run build
```

### 部署到服务器
将 `dist` 目录下的文件部署到Web服务器。

### Nginx配置示例
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/html;
    index index.html;
    
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 浏览器支持
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 开发团队
- 前端开发：React + TypeScript
- UI设计：Ant Design 5
- 状态管理：Zustand
- 构建工具：Vite

## 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证
MIT License

## 联系方式
如有问题或建议，请联系开发团队。

## 更新日志

### v1.0.0 (2024-01-20)
- ✅ 完成基础架构搭建
- ✅ 实现用户认证系统
- ✅ 完成仪表板页面
- ✅ 完成订单管理页面
- ✅ 完成黄金类型管理页面
- ✅ 实现响应式设计
- ✅ 添加主题定制支持
