import React from 'react';
import { Button, Result } from 'antd';
import { useNavigate } from 'react-router-dom';
import { HomeOutlined, RollbackOutlined } from '@ant-design/icons';
import '../../styles/PageContainer.css';

const NotFound: React.FC = () => {
  const navigate = useNavigate();

  const handleGoHome = () => {
    navigate('/');
  };

  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <div className="page-container">
      <div className="page-content">
        <div className="card-container" style={{ textAlign: 'center', padding: '60px 20px' }}>
          <Result
            status="404"
            title="404"
            subTitle="抱歉，您访问的页面不存在。"
            extra={[
              <Button 
                type="primary" 
                key="home" 
                icon={<HomeOutlined />}
                onClick={handleGoHome}
                size="large"
              >
                返回首页
              </Button>,
              <Button 
                key="back" 
                icon={<RollbackOutlined />}
                onClick={handleGoBack}
                size="large"
              >
                返回上页
              </Button>,
            ]}
          />
        </div>
      </div>
    </div>
  );
};

export default NotFound; 