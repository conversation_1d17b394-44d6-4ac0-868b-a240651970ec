import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Form, 
  Input, 
  Switch, 
  Button, 
  Space, 
  Row, 
  Col, 
  InputNumber, 
  Select, 
  Upload, 
  message, 
  Typography, 
  Divider, 
  ColorPicker,
  Alert,
  Tooltip,
  Badge,
  Statistic
} from 'antd';
import {
  SettingOutlined,
  DatabaseOutlined,
  SkinOutlined,
  BellOutlined,
  SecurityScanOutlined,
  SaveOutlined,
  ReloadOutlined,
  DownloadOutlined,
  UploadOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  UserOutlined,
  FileTextOutlined,
  GoldOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useTheme } from '../../contexts/ThemeContext';
import type { ThemeConfig } from '../../contexts/ThemeContext';
import '../../styles/PageContainer.css';

const { TabPane } = Tabs;
const { Option } = Select;
const { Title } = Typography;

interface SystemConfig {
  siteName: string;
  siteDescription: string;
  contactEmail: string;
  contactPhone: string;
  address: string;
  businessHours: string;
  maintenanceMode: boolean;
  allowRegistration: boolean;
  maxFileSize: number;
  sessionTimeout: number;
}

interface NotificationConfig {
  emailNotifications: boolean;
  smsNotifications: boolean;
  pushNotifications: boolean;
  orderUpdates: boolean;
  systemAlerts: boolean;
  marketingEmails: boolean;
}

const Settings: React.FC = () => {
  const { themeConfig, updateTheme, resetTheme } = useTheme();
  const [activeTab, setActiveTab] = useState('system');
  const [loading, setLoading] = useState(false);
  
  const [systemForm] = Form.useForm();
  const [themeForm] = Form.useForm();
  const [notificationForm] = Form.useForm();

  const defaultSystemConfig: SystemConfig = {
    siteName: '黄金回收管理系统',
    siteDescription: '专业、安全、高效的黄金回收管理平台',
    contactEmail: '<EMAIL>',
    contactPhone: '************',
    address: '北京市朝阳区xxx街道xxx号',
    businessHours: '周一至周五 9:00-18:00',
    maintenanceMode: false,
    allowRegistration: true,
    maxFileSize: 10,
    sessionTimeout: 30,
  };

  const defaultNotificationConfig: NotificationConfig = {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    orderUpdates: true,
    systemAlerts: true,
    marketingEmails: false,
  };

  useEffect(() => {
    // 加载配置
    loadConfigs();
  }, []);

  // 当主题配置变化时，同步到表单
  useEffect(() => {
    themeForm.setFieldsValue(themeConfig);
  }, [themeConfig, themeForm]);

  const loadConfigs = () => {
    // 这里应该从API加载配置，现在使用默认值
    systemForm.setFieldsValue(defaultSystemConfig);
    themeForm.setFieldsValue(themeConfig);
    notificationForm.setFieldsValue(defaultNotificationConfig);
  };

  const handleSystemSave = async (values: SystemConfig) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('系统配置保存成功！');
      console.log('系统配置:', values);
    } catch (error) {
      message.error('保存失败，请重试！');
    } finally {
      setLoading(false);
    }
  };

  const handleThemeSave = async (values: ThemeConfig) => {
    try {
      setLoading(true);
      // 实时应用主题配置
      updateTheme(values);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500));
      message.success('主题配置保存成功！');
      console.log('主题配置:', values);
    } catch (error) {
      message.error('保存失败，请重试！');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationSave = async (values: NotificationConfig) => {
    try {
      setLoading(true);
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      message.success('通知配置保存成功！');
      console.log('通知配置:', values);
    } catch (error) {
      message.error('保存失败，请重试！');
    } finally {
      setLoading(false);
    }
  };

  const handleThemeReset = () => {
    if (window.confirm('确定要重置主题配置到默认值吗？此操作不可恢复。')) {
      resetTheme();
      message.success('主题配置已重置为默认值！');
    }
  };

  const handleSystemReset = () => {
    if (window.confirm('确定要重置系统配置到默认值吗？此操作不可恢复。')) {
      systemForm.setFieldsValue(defaultSystemConfig);
      message.success('系统配置已重置为默认值！');
    }
  };

  const handleNotificationReset = () => {
    if (window.confirm('确定要重置通知配置到默认值吗？此操作不可恢复。')) {
      notificationForm.setFieldsValue(defaultNotificationConfig);
      message.success('通知配置已重置为默认值！');
    }
  };

  const handleExport = () => {
    const config = {
      system: systemForm.getFieldsValue(),
      theme: themeForm.getFieldsValue(),
      notification: notificationForm.getFieldsValue(),
      exportTime: new Date().toISOString(),
    };
    
    const blob = new Blob([JSON.stringify(config, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `system-config-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
    
    message.success('配置导出成功！');
  };

  const handleImport = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const config = JSON.parse(e.target?.result as string);
        if (config.system) systemForm.setFieldsValue(config.system);
        if (config.theme) {
          themeForm.setFieldsValue(config.theme);
          updateTheme(config.theme); // 立即应用导入的主题
        }
        if (config.notification) notificationForm.setFieldsValue(config.notification);
        message.success('配置导入成功！');
      } catch (error) {
        message.error('配置文件格式错误！');
      }
    };
    reader.readAsText(file);
    return false;
  };

  // 实时主题预览
  const handleThemeChange = (changedValues: any, allValues: ThemeConfig) => {
    // 实时预览主题变化
    updateTheme(allValues);
  };

  return (
    <div className="page-container">
      <div className="page-content">
        {/* 页面头部 */}
        <div className="page-header">
          <h1>系统设置</h1>
          <p>管理系统配置、主题设置、通知配置和安全设置等各项参数</p>
        </div>

        {/* 设置内容 */}
        <div className="card-container">
          <Card>
            <Tabs 
              activeKey={activeTab} 
              onChange={setActiveTab}
              type="card"
              size="large"
            >
              {/* 系统设置 */}
              <TabPane 
                tab={
                  <span>
                    <DatabaseOutlined />
                    系统设置
                  </span>
                } 
                key="system"
              >
                <Form
                  form={systemForm}
                  layout="vertical"
                  initialValues={defaultSystemConfig}
                  onFinish={handleSystemSave}
                >
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="siteName"
                        label="系统名称"
                        rules={[{ required: true, message: '请输入系统名称' }]}
                      >
                        <Input prefix={<SettingOutlined />} placeholder="请输入系统名称" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="siteDescription"
                        label="系统描述"
                        rules={[{ required: true, message: '请输入系统描述' }]}
                      >
                        <Input prefix={<FileTextOutlined />} placeholder="请输入系统描述" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="contactEmail"
                        label="联系邮箱"
                        rules={[
                          { required: true, message: '请输入联系邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' }
                        ]}
                      >
                        <Input prefix={<UserOutlined />} placeholder="请输入联系邮箱" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="contactPhone"
                        label="联系电话"
                        rules={[{ required: true, message: '请输入联系电话' }]}
                      >
                        <Input prefix={<UserOutlined />} placeholder="请输入联系电话" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    name="address"
                    label="公司地址"
                    rules={[{ required: true, message: '请输入公司地址' }]}
                  >
                    <Input prefix={<EnvironmentOutlined />} placeholder="请输入公司地址" />
                  </Form.Item>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="businessHours"
                        label="营业时间"
                        rules={[{ required: true, message: '请输入营业时间' }]}
                      >
                        <Input prefix={<ClockCircleOutlined />} placeholder="请输入营业时间" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="maxFileSize"
                        label="最大文件大小(MB)"
                        rules={[{ required: true, message: '请输入最大文件大小' }]}
                      >
                        <InputNumber 
                          prefix={<FileTextOutlined />} 
                          placeholder="请输入最大文件大小"
                          min={1}
                          max={100}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="sessionTimeout"
                        label="会话超时时间(分钟)"
                        rules={[{ required: true, message: '请输入会话超时时间' }]}
                      >
                        <InputNumber 
                          prefix={<ClockCircleOutlined />} 
                          placeholder="请输入会话超时时间"
                          min={5}
                          max={1440}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="maintenanceMode"
                        label="维护模式"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item
                    name="allowRegistration"
                    label="允许用户注册"
                    valuePropName="checked"
                  >
                    <Switch checkedChildren="允许" unCheckedChildren="禁止" />
                  </Form.Item>

                  <Form.Item>
                    <Space>
                      <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                        保存设置
                      </Button>
                      <Button icon={<ReloadOutlined />} onClick={handleSystemReset}>
                        重置
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              </TabPane>

              {/* 主题设置 */}
              <TabPane 
                tab={
                  <span>
                    <SkinOutlined />
                    主题设置
                  </span>
                } 
                key="theme"
              >
                <Form
                  form={themeForm}
                  layout="vertical"
                  initialValues={themeConfig}
                  onFinish={handleThemeSave}
                >
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="primaryColor"
                        label="主色调"
                        rules={[{ required: true, message: '请选择主色调' }]}
                      >
                        <ColorPicker 
                          showText
                          format="hex"
                          defaultValue={themeConfig.primaryColor}
                          onChange={(color) => {
                            themeForm.setFieldsValue({ primaryColor: color.toHexString() });
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="borderRadius"
                        label="圆角大小"
                        rules={[{ required: true, message: '请输入圆角大小' }]}
                      >
                        <InputNumber 
                          prefix={<SkinOutlined />} 
                          placeholder="请输入圆角大小"
                          min={0}
                          max={20}
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="darkMode"
                        label="深色模式"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="compactMode"
                        label="紧凑模式"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item>
                    <Space>
                      <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                        保存主题
                      </Button>
                      <Button icon={<ReloadOutlined />} onClick={handleThemeReset}>
                        重置主题
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              </TabPane>

              {/* 通知设置 */}
              <TabPane 
                tab={
                  <span>
                    <BellOutlined />
                    通知设置
                  </span>
                } 
                key="notification"
              >
                <Form
                  form={notificationForm}
                  layout="vertical"
                  initialValues={defaultNotificationConfig}
                  onFinish={handleNotificationSave}
                >
                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="emailNotifications"
                        label="邮件通知"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="smsNotifications"
                        label="短信通知"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="pushNotifications"
                        label="推送通知"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="orderUpdates"
                        label="订单更新通知"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={24}>
                    <Col span={12}>
                      <Form.Item
                        name="systemAlerts"
                        label="系统告警通知"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="marketingEmails"
                        label="营销邮件"
                        valuePropName="checked"
                      >
                        <Switch checkedChildren="开启" unCheckedChildren="关闭" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Form.Item>
                    <Space>
                      <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                        保存通知设置
                      </Button>
                      <Button icon={<ReloadOutlined />} onClick={handleNotificationReset}>
                        重置
                      </Button>
                    </Space>
                  </Form.Item>
                </Form>
              </TabPane>

              {/* 安全设置 */}
              <TabPane 
                tab={
                  <span>
                    <SecurityScanOutlined />
                    安全设置
                  </span>
                } 
                key="security"
              >
                <div style={{ padding: '20px 0' }}>
                  <Alert
                    message="安全设置"
                    description="这里可以配置系统的安全相关设置，包括密码策略、登录限制等。"
                    type="info"
                    showIcon
                    style={{ marginBottom: '24px' }}
                  />
                  
                  <Row gutter={24}>
                    <Col span={12}>
                      <Card title="密码策略" size="small">
                        <p>• 最小密码长度：8位</p>
                        <p>• 必须包含大小写字母</p>
                        <p>• 必须包含数字和特殊字符</p>
                        <p>• 密码有效期：90天</p>
                      </Card>
                    </Col>
                    <Col span={12}>
                      <Card title="登录限制" size="small">
                        <p>• 最大登录失败次数：5次</p>
                        <p>• 账户锁定时间：30分钟</p>
                        <p>• 会话超时时间：30分钟</p>
                        <p>• 强制密码修改：是</p>
                      </Card>
                    </Col>
                  </Row>
                </div>
              </TabPane>
            </Tabs>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Settings; 