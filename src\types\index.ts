// 用户信息
export interface User {
  id: string;
  username: string;
  phone: string;
  address: string;
  createdAt: string;
  updatedAt: string;
}

// 黄金类型
export interface GoldType {
  id: number;
  purityCode: string;
  itemCategory: string;
  pricePerG: number;
  status: number;
  effectiveFrom: string;
  remark?: string;
}

// 黄金类型DTO (用于前端表单)
export interface GoldTypeDTO {
  id?: number;
  purityCode: string;
  itemCategory: string;
  pricePerG: number;
  status: number;
  effectiveFrom: string;
  remark?: string;
}

// 订单信息
export interface Order {
  id: string;
  orderNumber: string;
  userId: string;
  userName: string;
  userPhone: string;
  userAddress: string;
  goldType: string;
  weight: number;
  estimatedPrice: number;
  finalPrice?: number;
  serviceMethod: '上门' | '到店' | '邮寄';
  serviceDate: string;
  serviceTime: string;
  status: 'pending' | 'confirmed' | 'processing' | 'completed' | 'cancelled';
  statusText: string;
  remarks?: string;
  images: string[];
  videoUrl?: string;
  inspectionReport?: string;
  createdAt: string;
  updatedAt: string;
}

// 估价记录
export interface Quote {
  id: string;
  userId?: string;
  goldType: string;
  weight: number;
  estimatedPrice: number;
  images: string[];
  createdAt: string;
}

// 视频质检记录
export interface VideoInspection {
  id: string;
  orderId: string;
  videoUrl: string;
  duration: number;
  status: 'pending' | 'processing' | 'completed';
  progress: number;
  inspectorId?: string;
  inspectorName?: string;
  inspectionNotes?: string;
  createdAt: string;
  updatedAt: string;
}

// 金价信息
export interface GoldPrice {
  id: string;
  goldType: string;
  price: number;
  change: number;
  changePercent: number;
  lastUpdated: string;
}

// 统计数据
export interface DashboardStats {
  totalOrders: number;
  pendingOrders: number;
  completedOrders: number;
  totalRevenue: number;
  monthlyRevenue: number;
  totalUsers: number;
  activeQuotes: number;
}

// API响应格式
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  code:number
}

// 分页参数
export interface PaginationParams {
  page: number;
  pageSize: number;
  total?: number;
}

// 搜索参数
export interface SearchParams {
  keyword?: string;
  status?: string;
  dateRange?: [string, string];
  goldType?: string;
}

// 表格列配置
export interface TableColumn {
  title: string;
  dataIndex: string;
  key: string;
  width?: number;
  render?: (value: any, record: any) => React.ReactNode;
  sorter?: boolean;
  filters?: Array<{ text: string; value: string }>;
} 