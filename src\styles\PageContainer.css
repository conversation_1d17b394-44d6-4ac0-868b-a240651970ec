/* 通用页面容器样式 */
.page-container {
  padding: 24px;
  background: #f0f2f5;
  min-height: 100vh;
  max-width: 100%;
  box-sizing: border-box;
}

/* 页面头部 */
.page-header {
  margin-bottom: 32px;
  text-align: center;
  padding: 0 16px;
}

.page-header h1 {
  margin-bottom: 12px;
  color: #1890ff;
  font-size: 28px;
  font-weight: 600;
  line-height: 1.4;
}

.page-header p {
  color: #666;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
  max-width: 600px;
  margin: 0 auto;
}

/* 内容区域 */
.page-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 16px;
}

/* 卡片容器 */
.card-container {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-container:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.card-container .ant-card-body {
  padding: 24px;
}

/* 统计卡片网格 */
.stats-grid {
  margin-bottom: 32px;
}

.stats-grid .ant-col {
  margin-bottom: 16px;
}

.stat-card {
  height: 100%;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-align: center;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-card .ant-statistic-title {
  color: #666;
  font-size: 14px;
  margin-bottom: 12px;
  font-weight: 500;
}

.stat-card .ant-statistic-content {
  font-size: 28px;
  font-weight: 700;
  line-height: 1.2;
}

/* 搜索和操作区域 */
.search-section {
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 16px;
}

.search-item {
  flex: 1;
  min-width: 200px;
}

.search-actions {
  display: flex;
  gap: 12px;
  align-items: center;
  flex-wrap: wrap;
}

/* 表格区域 */
.table-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-section .ant-card-body {
  padding: 0;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.selected-info {
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .page-container {
    padding: 20px;
  }
  
  .page-content {
    padding: 0 12px;
  }
  
  .search-section {
    padding: 20px;
  }
  
  .card-container .ant-card-body {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-header {
    margin-bottom: 24px;
    padding: 0 8px;
  }
  
  .page-header h1 {
    font-size: 24px;
  }
  
  .page-header p {
    font-size: 14px;
  }
  
  .page-content {
    padding: 0 8px;
  }
  
  .search-section {
    padding: 16px;
    margin-bottom: 20px;
  }
  
  .search-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-item {
    min-width: auto;
  }
  
  .search-actions {
    justify-content: stretch;
  }
  
  .action-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: center;
  }
  
  .card-container .ant-card-body {
    padding: 16px;
  }
  
  .stats-grid .ant-col {
    margin-bottom: 12px;
  }
}

@media (max-width: 576px) {
  .page-container {
    padding: 12px;
  }
  
  .page-header {
    margin-bottom: 20px;
    padding: 0 4px;
  }
  
  .page-header h1 {
    font-size: 20px;
  }
  
  .page-header p {
    font-size: 13px;
  }
  
  .page-content {
    padding: 0 4px;
  }
  
  .search-section {
    padding: 12px;
    margin-bottom: 16px;
  }
  
  .card-container .ant-card-body {
    padding: 12px;
  }
  
  .stats-grid .ant-col {
    margin-bottom: 8px;
  }
}

/* 深色模式支持 */
.dark-mode .page-container {
  background: #141414;
}

.dark-mode .page-header h1 {
  color: #1890ff;
}

.dark-mode .page-header p {
  color: #a6adb4;
}

.dark-mode .search-section,
.dark-mode .table-section,
.dark-mode .card-container {
  background: #1f1f1f;
  border-color: #434343;
}

.dark-mode .stat-card {
  background: #1f1f1f;
  border-color: #434343;
}

.dark-mode .stat-card .ant-statistic-title {
  color: #a6adb4;
}

.dark-mode .selected-info {
  color: #a6adb4;
}

/* 紧凑模式支持 */
.compact-mode .page-container {
  padding: 16px;
}

.compact-mode .page-header {
  margin-bottom: 24px;
}

.compact-mode .search-section {
  padding: 16px;
  margin-bottom: 20px;
}

.compact-mode .card-container .ant-card-body {
  padding: 16px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .card-container,
  .search-section,
  .table-section {
    border: 2px solid #000;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .card-container,
  .stat-card {
    transition: none;
  }
  
  .card-container:hover,
  .stat-card:hover {
    transform: none;
  }
}

/* 打印样式 */
@media print {
  .page-container {
    background: white;
    padding: 0;
  }
  
  .search-section,
  .action-section {
    display: none;
  }
  
  .card-container,
  .table-section {
    box-shadow: none;
    border: 1px solid #000;
    break-inside: avoid;
  }
} 