import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  // server: {
  //   host:'************',
  //   proxy: {
  //     '/api': {
  //       // target: 'https://qinqinjava.cn:8081',
  //       // target: 'http://************:8082',
  //       target: 'http://************:8085',
  //       changeOrigin: true,
  //       rewrite: (path) => path.replace(/^\/api/, '') // 重写路径，去掉 '/api' 前缀
  //     }
  //   }
  // }
})
