import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import MainLayout from './components/Layout/MainLayout';
import Dashboard from './pages/Dashboard/Dashboard';
import Orders from './pages/Orders/Orders';
import GoldTypes from './pages/GoldTypes/GoldTypes';
import Quotes from './pages/Quotes/Quotes';
import VideoInspection from './pages/VideoInspection/VideoInspection';
import Users from './pages/Users/<USER>';
import Settings from './pages/Settings/Settings';
import Login from './pages/Login/Login';
import NotFound from './pages/NotFound/NotFound';
import { useAppStore } from './store';
import { ThemeProvider } from './contexts/ThemeContext';
import './App.css';

const App: React.FC = () => {
  const { isAuthenticated, initAuth } = useAppStore();

  // 初始化认证状态
  useEffect(() => {
    initAuth();
  }, [initAuth]);

  return (
    <ThemeProvider>
      <ConfigProvider locale={zhCN}>
        <Router>
          <div className="app">
            <Routes>
              <Route path="/login" element={<Login />} />
              
              <Route path="/" element={
                isAuthenticated ? <MainLayout /> : <Navigate to="/login" replace />
              }>
                <Route index element={<Dashboard />} />
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="orders" element={<Orders />} />
                <Route path="gold-types" element={<GoldTypes />} />
                <Route path="quotes" element={<Quotes />} />
                <Route path="video-inspection" element={<VideoInspection />} />
                <Route path="users" element={<Users />} />
                <Route path="settings" element={<Settings />} />
              </Route>
              
              {/* 404 页面 */}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </div>
        </Router>
      </ConfigProvider>
    </ThemeProvider>
  );
};

export default App;
