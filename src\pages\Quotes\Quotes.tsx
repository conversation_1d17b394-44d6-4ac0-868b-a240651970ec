import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Space, 
  Input, 
  Select, 
  DatePicker, 
  Tag, 
  Modal, 
  message,
  Tooltip,
  Row,
  Col,
  Statistic,
  Image,
  Drawer,
  Alert,
  Popconfirm
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ExportOutlined,
  PictureOutlined,
  CalculatorOutlined,
  FileTextOutlined,
  GoldOutlined,
  DollarOutlined,
  CalendarOutlined
} from '@ant-design/icons';
import { quoteAPI } from '../../services/api';
import type { Quote, SearchParams, PaginationParams } from '../../types';
import dayjs from 'dayjs';
import '../../styles/PageContainer.css';

const { RangePicker } = DatePicker;
const { Option } = Select;

const Quotes: React.FC = () => {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [imagePreviewVisible, setImagePreviewVisible] = useState(false);
  const [previewImages, setPreviewImages] = useState<string[]>([]);
  const [previewIndex, setPreviewIndex] = useState(0);

  useEffect(() => {
    loadQuotes();
  }, [pagination.page, pagination.pageSize, searchParams]);

  const loadQuotes = async () => {
    try {
      setLoading(true);
      const params = {
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...searchParams,
      };
      
      const response = await quoteAPI.getQuotes(params);
      if (response.success) {
        setQuotes(response.data.quotes);
        setPagination(prev => ({
          ...prev,
          total: response.data.total,
        }));
      }
    } catch (error) {
      console.error('加载估价记录失败:', error);
      message.error('加载估价记录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleReset = () => {
    setSearchParams({});
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    setPagination(prev => ({
      ...prev,
      page: pagination.current,
      pageSize: pagination.pageSize,
    }));
  };

  const handleDelete = async (id: string) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这条估价记录吗？此操作不可恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await quoteAPI.deleteQuote(id);
          if (response.success) {
            message.success('删除成功');
            loadQuotes();
          } else {
            message.error(response.message || '删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  const handleExport = () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请先选择要导出的记录');
      return;
    }
    message.info('导出功能开发中');
  };

  const handleRefresh = () => {
    loadQuotes();
    message.success('数据已刷新');
  };

  const handleImagePreview = (images: string[], index: number = 0) => {
    setPreviewImages(images);
    setPreviewIndex(index);
    setImagePreviewVisible(true);
  };

  const handlePreviewClose = () => {
    setImagePreviewVisible(false);
    setPreviewImages([]);
    setPreviewIndex(0);
  };

  const handlePreviewChange = (current: number) => {
    setPreviewIndex(current);
  };

  // 计算统计数据
  const totalQuotes = quotes.length;
  const totalWeight = quotes.reduce((sum, quote) => sum + (quote.weight || 0), 0);
  const totalValue = quotes.reduce((sum, quote) => sum + (quote.estimatedPrice || 0), 0);
  const avgPrice = totalQuotes > 0 ? totalValue / totalQuotes : 0;

  const columns = [
    {
      title: '估价ID',
      dataIndex: 'id',
      key: 'id',
      render: (text: string) => (
        <span style={{ fontFamily: 'monospace', color: '#1890ff', fontWeight: 600 }}>
          {text}
        </span>
      ),
    },
    {
      title: '黄金类型',
      dataIndex: 'goldType',
      key: 'goldType',
      render: (text: string) => (
        <Tag color="gold" icon={<GoldOutlined />}>
          {text}
        </Tag>
      ),
    },
    {
      title: '重量(g)',
      dataIndex: 'weight',
      key: 'weight',
      render: (text: number) => (
        <span style={{ fontWeight: 600 }}>{text?.toFixed(2)}</span>
      ),
      sorter: (a: any, b: any) => (a.weight || 0) - (b.weight || 0),
    },
    {
      title: '预估价格',
      dataIndex: 'estimatedPrice',
      key: 'estimatedPrice',
      render: (text: number) => (
        <span style={{ color: '#52c41a', fontWeight: 600 }}>
          ¥{text?.toLocaleString()}
        </span>
      ),
      sorter: (a: any, b: any) => (a.estimatedPrice || 0) - (b.estimatedPrice || 0),
    },
    {
      title: '图片数量',
      dataIndex: 'images',
      key: 'images',
      render: (images: string[]) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <PictureOutlined style={{ color: '#1890ff' }} />
          {images?.length || 0}
        </div>
      ),
      sorter: (a: any, b: any) => (a.images?.length || 0) - (b.images?.length || 0),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (text: string) => (
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <CalendarOutlined style={{ color: '#666' }} />
          {dayjs(text).format('YYYY-MM-DD HH:mm')}
        </div>
      ),
      sorter: (a: any, b: any) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record: any) => (
        <Space size="small">
          <Button 
            type="text" 
            icon={<EyeOutlined />} 
            size="small"
            onClick={() => handleImagePreview(record.images || [], 0)}
          >
            查看图片
          </Button>
          <Popconfirm
            title="确定要删除这条估价记录吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              icon={<DeleteOutlined />} 
              size="small"
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys as string[]);
    },
  };

  return (
    <div className="page-container">
      <div className="page-content">
        {/* 页面头部 */}
        <div className="page-header">
          <h1>估价记录管理</h1>
          <p>管理所有黄金估价记录和图片，支持查看、删除和导出功能</p>
        </div>

        {/* 错误提示 */}
        {loading && (
          <Alert
            message="加载估价记录失败"
            description="请检查网络连接或联系管理员"
            type="error"
            showIcon
            style={{ marginBottom: '24px' }}
            closable
          />
        )}

        {/* 统计信息 */}
        <div className="stats-grid">
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总记录数"
                  value={totalQuotes}
                  valueStyle={{ color: '#1890ff' }}
                  prefix={<FileTextOutlined />}
                  suffix="条"
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总重量"
                  value={totalWeight}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<GoldOutlined />}
                  suffix="g"
                  formatter={(value) => value.toFixed(2)}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="总价值"
                  value={totalValue}
                  valueStyle={{ color: '#722ed1' }}
                  prefix={<DollarOutlined />}
                  suffix="元"
                  formatter={(value) => `${(value / 10000).toFixed(1)}万`}
                />
              </Card>
            </Col>
            <Col xs={24} sm={12} lg={6}>
              <Card className="stat-card">
                <Statistic
                  title="平均价格"
                  value={avgPrice}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<CalculatorOutlined />}
                  suffix="¥"
                  formatter={(value) => value.toFixed(2)}
                />
              </Card>
            </Col>
          </Row>
        </div>

        {/* 搜索和操作区域 */}
        <div className="search-section">
          <div className="search-row">
            <div className="search-item">
              <Input
                placeholder="搜索估价ID或黄金类型"
                prefix={<SearchOutlined />}
                value={searchParams.keyword || ''}
                onChange={(e) => setSearchParams(prev => ({ ...prev, keyword: e.target.value }))}
                allowClear
              />
            </div>
            <div className="search-item">
              <Select
                placeholder="选择黄金类型"
                value={searchParams.goldType || 'all'}
                onChange={(value) => setSearchParams(prev => ({ ...prev, goldType: value }))}
                style={{ width: '100%' }}
              >
                <Option value="all">全部类型</Option>
                <Option value="足金999">足金999</Option>
                <Option value="足金990">足金990</Option>
                <Option value="K金18K">K金18K</Option>
                <Option value="K金14K">K金14K</Option>
                <Option value="金条">金条</Option>
              </Select>
            </div>
            <div className="search-item">
              <RangePicker
                placeholder={['开始日期', '结束日期']}
                value={searchParams.dateRange ? [dayjs(searchParams.dateRange[0]), dayjs(searchParams.dateRange[1])] : null}
                onChange={(dates) => setSearchParams(prev => ({ 
                  ...prev, 
                  dateRange: dates ? [dates[0]?.toDate(), dates[1]?.toDate()] : undefined 
                }))}
                style={{ width: '100%' }}
              />
            </div>
          </div>
          <div className="search-actions">
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              搜索
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>
              重置
            </Button>
          </div>
        </div>

        {/* 操作按钮区域 */}
        <div className="action-section">
          <div className="action-buttons">
            <Button icon={<ExportOutlined />} onClick={handleExport} disabled={selectedRowKeys.length === 0}>
              导出数据
            </Button>
            <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
              刷新数据
            </Button>
          </div>
          <div className="selected-info">
            已选择 {selectedRowKeys.length} 项
          </div>
        </div>

        {/* 表格区域 */}
        <div className="table-section">
          <Card>
            <Table
              columns={columns}
              dataSource={quotes}
              rowKey="id"
              loading={loading}
              rowSelection={rowSelection}
              pagination={{
                current: pagination.page,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({ ...prev, page, pageSize: pageSize || 10 }));
                },
              }}
              onChange={handleTableChange}
              scroll={{ x: 1200 }}
            />
          </Card>
        </div>

        {/* 图片预览 */}
        <Image.PreviewGroup
          preview={{
            visible: imagePreviewVisible,
            onVisibleChange: (visible) => {
              if (!visible) handlePreviewClose();
            },
            current: previewIndex,
            onChange: handlePreviewChange,
          }}
        >
          {previewImages.map((image, index) => (
            <Image
              key={index}
              src={image}
              style={{ display: 'none' }}
            />
          ))}
        </Image.PreviewGroup>
      </div>
    </div>
  );
};

export default Quotes; 