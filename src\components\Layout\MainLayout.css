.main-layout {
  min-height: 100vh;
  display: flex;
}

.main-sider {
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  transition: all 0.2s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  width: 200px !important;
  background: #ffffff;
}

.main-sider.ant-layout-sider-collapsed {
  width: 80px !important;
}

.main-sider .ant-layout-sider-children {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 默认白色主题的Logo */
.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ffffff;
  color: var(--primary-color, #1890ff);
  font-size: 20px;
  font-weight: bold;
  border-bottom: 1px solid #f0f0f0;
  transition: all 0.2s ease;
}

.logo.collapsed {
  font-size: 16px;
}

.logo-text {
  margin-left: 12px;
  color: var(--primary-color, #1890ff);
  font-weight: 600;
}

/* 默认白色主题的菜单 */
.main-menu {
  flex: 1;
  border-right: none;
  background: #ffffff;
}

.main-menu .ant-menu-item,
.main-menu .ant-menu-submenu-title {
  color: #595959;
  transition: all 0.2s ease;
}

.main-menu .ant-menu-item:hover,
.main-menu .ant-menu-submenu-title:hover {
  color: var(--primary-color, #1890ff);
  background: color-mix(in srgb, var(--primary-color, #1890ff) 10%, transparent);
}

.main-menu .ant-menu-item-selected {
  background: var(--primary-color, #1890ff);
  color: white;
}

.main-menu .ant-menu-item-selected:hover {
  background: color-mix(in srgb, var(--primary-color, #1890ff) 80%, white);
  color: white;
}

.main-menu .ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: var(--primary-color, #1890ff);
}

.main-menu .ant-menu-submenu-open > .ant-menu-submenu-title {
  color: var(--primary-color, #1890ff);
}

.username {
  color: #595959;
  font-size: 14px;
  margin-left: 8px;
  transition: all 0.2s ease;
}

/* 主内容区域 - 关键修复 */
.main-content {
  margin-left: 200px;
  transition: margin-left 0.2s ease;
  min-height: 100vh;
  background: #f0f2f5;
  width: calc(100vw - 200px);
}

.main-content.collapsed {
  margin-left: 80px;
  width: calc(100vw - 80px);
}

.main-header {
  background: white;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 999;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 消息通知按钮样式 */
.notification-btn {
  transition: all 0.2s ease;
}

.notification-btn:hover {
  background: #f5f5f5;
  transform: scale(1.05);
}

.dark-mode .notification-btn:hover {
  background: #262626;
}

/* 用户信息样式 */
.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 0px 12px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.user-info:hover {
  background: #f5f5f5;
}

.user-info:active {
  transform: scale(0.98);
}

.user-avatar {
  background: var(--primary-color, #1890ff);
  color: white;
  font-weight: bold;
}

/* 深色模式支持 */
.dark-mode .main-sider {
  background: #001529;
}

.dark-mode .logo {
  background: #002140;
  border-bottom-color: #003a70;
  color: var(--primary-color, #1890ff);
}

.dark-mode .logo-text {
  color: var(--primary-color, #1890ff);
}

.dark-mode .main-menu {
  background: #001529;
}

.dark-mode .main-menu .ant-menu-item,
.dark-mode .main-menu .ant-menu-submenu-title {
  color: #a6adb4;
}

.dark-mode .main-menu .ant-menu-item:hover,
.dark-mode .main-menu .ant-menu-submenu-title:hover {
  color: var(--primary-color, #1890ff);
  background: color-mix(in srgb, var(--primary-color, #1890ff) 20%, #1f1f1f);
}

.dark-mode .main-menu .ant-menu-item-selected {
  background: var(--primary-color, #1890ff);
  color: white;
}

.dark-mode .main-menu .ant-menu-item-selected:hover {
  background: color-mix(in srgb, var(--primary-color, #1890ff) 80%, #1f1f1f);
  color: white;
}

.dark-mode .main-menu .ant-menu-submenu-selected > .ant-menu-submenu-title {
  color: var(--primary-color, #1890ff);
}

.dark-mode .main-menu .ant-menu-submenu-open > .ant-menu-submenu-title {
  color: var(--primary-color, #1890ff);
}

.dark-mode .username {
  color: #a6adb4;
}

.dark-mode .main-content {
  background: #141414;
}

.dark-mode .main-header {
  background: #1f1f1f;
  border-bottom: 1px solid #434343;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
}

.dark-mode .user-info:hover {
  background: #262626;
}

/* 下拉菜单样式 */
.ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.ant-dropdown-menu-item {
  padding: 8px 16px;
  transition: all 0.2s ease;
}

.ant-dropdown-menu-item:hover {
  background: #f5f5f5;
}

.dark-mode .ant-dropdown-menu {
  background: #1f1f1f;
  border: 1px solid #434343;
}

.dark-mode .ant-dropdown-menu-item {
  color: #d9d9d9;
}

.dark-mode .ant-dropdown-menu-item:hover {
  background: #262626;
}

/* 消息徽章样式 */
.ant-badge-count {
  background: #ff4d4f;
  box-shadow: 0 0 0 1px #fff;
}

.dark-mode .ant-badge-count {
  box-shadow: 0 0 0 1px #1f1f1f;
}

/* 紧凑模式支持 */
.compact-mode .main-sider {
  width: 160px !important;
}

.compact-mode .main-content {
  margin-left: 160px;
  width: calc(100vw - 160px);
}

.compact-mode .main-content.collapsed {
  margin-left: 64px;
  width: calc(100vw - 64px);
}

.compact-mode .logo {
  height: 48px;
  font-size: 16px;
}

.compact-mode .logo.collapsed {
  font-size: 14px;
}

.compact-mode .main-header {
  padding: 0 16px;
  height: 48px;
}

.compact-mode .main-menu .ant-menu-item,
.compact-mode .main-menu .ant-menu-submenu-title {
  height: 40px;
  line-height: 40px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-sider {
    transform: translateX(-100%);
  }
  
  .main-sider.mobile-open {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
    width: 100vw;
  }
  
  .main-content.collapsed {
    margin-left: 0;
    width: 100vw;
  }
  
  .main-header {
    padding: 0 16px;
  }
  
  .header-right {
    gap: 12px;
  }
  
  .user-info {
    padding: 6px 8px;
  }
}

@media (max-width: 576px) {
  .main-header {
    padding: 0 12px;
  }
  
  .header-right {
    gap: 8px;
  }
  
  .username {
    display: none;
  }
  
  .user-info {
    padding: 4px 6px;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .main-sider {
    border-right: 2px solid #000;
  }
  
  .logo {
    border-bottom: 2px solid #000;
  }
  
  .main-menu .ant-menu-item-selected {
    border: 2px solid #000;
  }
  
  .main-header {
    border-bottom: 2px solid #000;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .main-sider,
  .main-content,
  .logo,
  .main-menu .ant-menu-item,
  .main-menu .ant-menu-submenu-title,
  .username,
  .user-info {
    transition: none;
  }
}

/* 打印样式 */
@media print {
  .main-sider {
    display: none;
  }
  
  .main-content {
    margin-left: 0;
    width: 100%;
  }
  
  .main-header {
    display: none;
  }
} 

/* 深色模式下的弹窗样式 */
.dark-mode .ant-modal-content {
  background: #1f1f1f;
  border: 1px solid #434343;
}

.dark-mode .ant-modal-header {
  background: #262626;
  border-bottom-color: #434343;
}

.dark-mode .ant-modal-title {
  color: #d9d9d9;
}

.dark-mode .ant-modal-body {
  color: #d9d9d9;
}

.dark-mode .ant-modal-footer {
  border-top-color: #434343;
}

.dark-mode .ant-btn {
  border-color: #434343;
}

.dark-mode .ant-btn-default {
  background: #1f1f1f;
  color: #d9d9d9;
}

.dark-mode .ant-btn-default:hover {
  background: #262626;
  border-color: var(--primary-color, #1890ff);
  color: var(--primary-color, #1890ff);
}

/* 深色模式下的消息内容样式 */
.dark-mode .ant-modal-body div {
  color: #d9d9d9;
}

.dark-mode .ant-modal-body div[style*="color: #666"] {
  color: #a6adb4 !important;
}

.dark-mode .ant-modal-body div[style*="background: #f5f5f5"] {
  background: #262626 !important;
} 