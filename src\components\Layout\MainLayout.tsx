import React, { useState, useEffect } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space, Badge, theme, message, Modal } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  ShoppingCartOutlined,
  GoldOutlined,
  CalculatorOutlined,
  VideoCameraOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
  UserSwitchOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation, Outlet } from 'react-router-dom';
import { useAppStore } from '../../store';
import { useTheme } from '../../contexts/ThemeContext';
import './MainLayout.css';

const { Header, Sider, Content } = Layout;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser, logout, setSidebarCollapsed } = useAppStore();
  const { themeConfig } = useTheme();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 检测深色模式
  const isDarkMode = themeConfig.darkMode;

  useEffect(() => {
    setSidebarCollapsed(collapsed);
  }, [collapsed, setSidebarCollapsed]);

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key);
  };

  const handleLogout = () => {
    Modal.confirm({
      title: '确认退出',
      content: '您确定要退出登录吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        logout();
        navigate('/login');
        message.success('已成功退出登录');
      },
    });
  };

  // 处理消息通知点击
  const handleNotificationClick = () => {
    // 显示消息列表
    Modal.info({
      title: '消息通知',
      width: 500,
      content: (
        <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
          <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>
            <div style={{ fontWeight: 'bold', color: '#1890ff' }}>系统通知</div>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>2024-01-15 10:30</div>
            <div style={{ marginTop: '8px' }}>系统维护完成，所有功能已恢复正常。</div>
          </div>
          <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>
            <div style={{ fontWeight: 'bold', color: '#52c41a' }}>订单更新</div>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>2024-01-15 09:15</div>
            <div style={{ marginTop: '8px' }}>订单 #ORD001 状态已更新为已完成。</div>
          </div>
          <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>
            <div style={{ fontWeight: 'bold', color: '#faad14' }}>价格提醒</div>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>2024-01-15 08:00</div>
            <div style={{ marginTop: '8px' }}>黄金价格有变动，建议及时关注市场动态。</div>
          </div>
          <div style={{ padding: '12px 0', borderBottom: '1px solid #f0f0f0' }}>
            <div style={{ fontWeight: 'bold', color: '#722ed1' }}>新用户注册</div>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>2024-01-14 16:45</div>
            <div style={{ marginTop: '8px' }}>新用户 张三 已完成注册。</div>
          </div>
          <div style={{ padding: '12px 0' }}>
            <div style={{ fontWeight: 'bold', color: '#ff4d4f' }}>安全提醒</div>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>2024-01-14 14:20</div>
            <div style={{ marginTop: '8px' }}>检测到异常登录尝试，请及时检查账户安全。</div>
          </div>
        </div>
      ),
      okText: '关闭',
      onOk: () => {
        message.success('已查看所有消息');
      },
    });
  };

  // 处理个人资料点击
  const handleProfileClick = () => {
    Modal.info({
      title: '个人资料',
      width: 600,
      content: (
        <div style={{ padding: '20px 0' }}>
          <div style={{ textAlign: 'center', marginBottom: '24px' }}>
            <Avatar 
              size={80} 
              icon={<UserOutlined />}
              style={{ backgroundColor: '#1890ff', marginBottom: '16px' }}
            />
            <div style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '8px' }}>
              {currentUser?.username || '管理员'}
            </div>
            <div style={{ color: '#666' }}>系统管理员</div>
          </div>
          
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>用户名</div>
              <div style={{ color: '#666' }}>{currentUser?.username || 'admin'}</div>
            </div>
            <div>
              <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>角色</div>
              <div style={{ color: '#666' }}>系统管理员</div>
            </div>
            <div>
              <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>邮箱</div>
              <div style={{ color: '#666' }}><EMAIL></div>
            </div>
            <div>
              <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>手机号</div>
              <div style={{ color: '#666' }}>138****8888</div>
            </div>
            <div>
              <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>最后登录</div>
              <div style={{ color: '#666' }}>2024-01-15 09:30</div>
            </div>
            <div>
              <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>登录IP</div>
              <div style={{ color: '#666' }}>*************</div>
            </div>
          </div>
          
          <div style={{ marginTop: '24px', padding: '16px', background: '#f5f5f5', borderRadius: '6px' }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>账户状态</div>
            <div style={{ color: '#52c41a' }}>● 正常</div>
            <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
              账户安全，无异常登录记录
            </div>
          </div>
        </div>
      ),
      okText: '关闭',
      cancelText: '编辑资料',
      onCancel: () => {
        message.info('编辑资料功能开发中');
      },
    });
  };

  // 处理设置点击
  const handleSettingsClick = () => {
    navigate('/settings');
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
      onClick: handleProfileClick,
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
      onClick: handleSettingsClick,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表盘',
    },
    {
      key: '/orders',
      icon: <ShoppingCartOutlined />,
      label: '订单管理',
    },
    {
      key: '/gold-types',
      icon: <GoldOutlined />,
      label: '黄金类型',
    },
    {
      key: '/quotes',
      icon: <CalculatorOutlined />,
      label: '估价记录',
    },
    {
      key: '/video-inspection',
      icon: <VideoCameraOutlined />,
      label: '视频质检',
    },
    {
      key: '/users',
      icon: <UserOutlined />,
      label: '用户管理',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  const getSelectedKeys = () => {
    const pathname = location.pathname;
    if (pathname === '/') return ['/dashboard'];
    return [pathname];
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        theme={isDarkMode ? "dark" : "light"}
        className={`main-sider ${isDarkMode ? 'dark-mode' : ''}`}
        width={200}
      >
        <div className={`logo ${isDarkMode ? 'dark-mode' : ''}`}>
          <GoldOutlined style={{ fontSize: '24px', color: themeConfig.primaryColor }} />
          {!collapsed && <span className="logo-text">黄金回收管理</span>}
        </div>
        
        <Menu
          theme={isDarkMode ? "dark" : "light"}
          mode="inline"
          selectedKeys={getSelectedKeys()}
          items={menuItems}
          onClick={handleMenuClick}
          className={`main-menu ${isDarkMode ? 'dark-mode' : ''}`}
        />
      </Sider>
      
      <Layout className={`main-content ${collapsed ? 'collapsed' : ''}`}>
        <Header 
          className="main-header"
          style={{ 
            padding: '0 24px', 
            background: colorBgContainer,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
            position: 'fixed',
            width: '-webkit-fill-available'
          }}
        >
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 64,
              height: 64,
            }}
          />
          
          <Space size="large">
            <Badge count={5} size="small">
              <Button 
                type="text" 
                icon={<BellOutlined />} 
                style={{ fontSize: '18px' }}
                onClick={handleNotificationClick}
                className="notification-btn"
              />
            </Badge>
            
            <Dropdown
              menu={{ items: userMenuItems }}
              placement="bottomRight"
              arrow
              trigger={['click']}
            >
              <Space className="user-info">
                <Avatar 
                  size="small" 
                  icon={<UserOutlined />}
                  style={{ backgroundColor: '#1890ff' }}
                />
                {!collapsed && (
                  <span className="username">
                    {currentUser?.username || '管理员'}
                  </span>
                )}
              </Space>
            </Dropdown>
          </Space>
        </Header>
        
        <Content
          style={{
            margin: '0',
            padding: '0',
            background: 'transparent',
            minHeight: 'calc(100vh - 64px)',
            overflow: 'visible',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout; 